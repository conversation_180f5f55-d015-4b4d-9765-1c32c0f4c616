/**
 * Tesseract Service for OCR processing
 * Handles text extraction from images and PDFs using Tesseract.js
 */

import { createWorker, createScheduler } from 'tesseract.js';
import LanguagesMapping from '../core/config/languageMappings';

class TesseractService {
  constructor() {
    this.scheduler = null;
    this.workers = [];
    this.languageMapping = new LanguagesMapping();
    this.initialized = false;
  }

  /**
   * Initialize Tesseract workers
   * @param {number} numWorkers - Number of workers to create (default: 2)
   * @returns {Promise<void>}
   */
  async initialize(numWorkers = 2) {
    if (this.initialized) return;

    try {
      this.scheduler = createScheduler();

      // Create workers
      for (let i = 0; i < numWorkers; i++) {
        const worker = await createWorker({
          logger: progress => {
            if (progress.status === 'recognizing text') {
              console.log(`OCR Progress: ${(progress.progress * 100).toFixed(2)}%`);
            }
          }
        });

        // Initialize worker with supported languages
        await worker.loadLanguage('eng+pol');
        await worker.initialize('eng+pol');

        this.workers.push(worker);
        this.scheduler.addWorker(worker);
      }

      this.initialized = true;
    } catch (error) {
      console.error('Error initializing Tesseract:', error);
      throw error;
    }
  }

  /**
   * Extract text from an image or PDF page
   * @param {File|Blob} file - File to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} OCR results with confidence scores
   */
  async extractText(file, options = {}) {
    if (!this.initialized) {
      await this.initialize();
    }

    try {
      // Convert file to image data URL
      const imageUrl = await this.fileToImageUrl(file);

      // Perform OCR with scheduler for better performance
      const result = await this.scheduler.addJob('recognize', imageUrl);

      return this.processOcrResult(result);
    } catch (error) {
      console.error('Error in Tesseract OCR:', error);
      throw error;
    }
  }

  /**
   * Process OCR result and extract structured data
   * @param {Object} result - Raw OCR result
   * @returns {Object} Processed OCR data
   */
  processOcrResult(result) {
    const { data } = result;

    // Extract words with confidence scores and positions
    const words = data.words.map(word => ({
      text: word.text,
      confidence: word.confidence,
      bbox: word.bbox,
      line: word.line
    }));

    // Group words by lines
    const lines = data.lines.map(line => ({
      text: line.text,
      confidence: line.confidence,
      bbox: line.bbox,
      words: words.filter(w => w.line === line)
    }));

    return {
      text: data.text,
      words,
      lines,
      confidence: data.confidence,
      language: this.languageMapping.convertOcrToDocumentLanguage(data.language),
      orientation: data.orientation,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Convert file to image URL for OCR processing
   * @param {File|Blob} file - File to convert
   * @returns {Promise<string>} Image data URL
   */
  async fileToImageUrl(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          // Convert ArrayBuffer to base64 string if needed
          const bytes = new Uint8Array(reader.result);
          let binary = '';
          for (let i = 0; i < bytes.byteLength; i++) {
            binary += String.fromCharCode(bytes[i]);
          }
          resolve('data:image/png;base64,' + btoa(binary));
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Clean up resources
   */
  async terminate() {
    if (this.scheduler) {
      await this.scheduler.terminate();
    }

    for (const worker of this.workers) {
      await worker.terminate();
    }

    this.workers = [];
    this.initialized = false;
  }

  /**
   * Get supported languages
   * @returns {Array<string>} Array of supported language codes
   */
  getSupportedLanguages() {
    return this.languageMapping.getOcrLanguageTypeFormat().split('|');
  }

  /**
   * Set recognition languages
   * @param {string|Array<string>} languages - Language code(s) to use
   * @returns {Promise<void>}
   */
  async setLanguages(languages) {
    if (!this.initialized) {
      await this.initialize();
    }

    const langString = Array.isArray(languages) ? languages.join('+') : languages;

    for (const worker of this.workers) {
      await worker.loadLanguage(langString);
      await worker.initialize(langString);
    }
  }
}

export default TesseractService;
