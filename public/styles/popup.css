/* MVAT Chrome Extension Popup Styles */
/* CSP Compliant - No inline styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 400px;
  min-height: 600px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background-color: #ffffff;
  color: #1f2937;
  overflow-x: hidden;
}

#root {
  width: 100%;
  height: 100%;
  min-height: 600px;
}

/* Removed loading spinner - now handled by React components */

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #6b7280;
  font-size: 14px;
  text-align: center;
}

/* Error fallback */
.error-fallback {
  padding: 20px;
  text-align: center;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
  margin: 20px;
}

.error-title {
  font-size: 16px;
  font-weight: 600;
  color: #dc2626;
  margin-bottom: 8px;
}

.error-message {
  font-size: 14px;
  color: #7f1d1d;
  margin-bottom: 16px;
}

.error-button {
  background: #dc2626;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.error-button:hover {
  background: #b91c1c;
}

/* MVAT App Styles */
.mvat-app {
  width: 100%;
  min-height: 600px;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mvat-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.mvat-logo {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #4F46E5, #7C3AED);
  border-radius: 8px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.mvat-title {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.status-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
}

.status-card.warning {
  background: #fef3c7;
  border: 1px solid #fbbf24;
}

.status-card.success {
  background: #ecfdf5;
  border: 1px solid #10b981;
}

.status-card.error {
  background: #fef2f2;
  border: 1px solid #ef4444;
}

.card-title {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.card-title.warning {
  color: #92400e;
}

.card-title.success {
  color: #065f46;
}

.card-title.error {
  color: #dc2626;
}

.card-text {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.card-text.warning {
  color: #92400e;
}

.card-text.success {
  color: #065f46;
}

.card-text.error {
  color: #dc2626;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.test-button {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
}

.test-button.primary {
  background: #3b82f6;
  color: white;
}

.test-button.primary:hover {
  background: #2563eb;
}

.test-button.success {
  background: #10b981;
  color: white;
}

.test-button.success:hover {
  background: #059669;
}

.test-button.warning {
  background: #f59e0b;
  color: white;
}

.test-button.warning:hover {
  background: #d97706;
}

.test-results {
  background: #f1f5f9;
  border-radius: 6px;
  padding: 12px;
  font-size: 11px;
  color: #475569;
  min-height: 60px;
}

.results-title {
  margin: 0;
  font-weight: 500;
}

.results-text {
  margin: 4px 0 0 0;
}

.results-success {
  color: #059669;
}

.results-error {
  color: #dc2626;
}

.footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  font-size: 11px;
  color: #6b7280;
  text-align: center;
}

.footer-text {
  margin: 0;
}

.footer-subtext {
  margin: 4px 0 0 0;
}

/* Utility classes */
.hidden {
  display: none !important;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.text-center {
  text-align: center;
}

.font-semibold {
  font-weight: 600;
}

.text-sm {
  font-size: 14px;
}

.text-xs {
  font-size: 12px;
}

.mb-4 {
  margin-bottom: 16px;
}

.p-4 {
  padding: 16px;
}

.rounded {
  border-radius: 6px;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-green-500 {
  background-color: #10b981;
}

.text-white {
  color: white;
}

.border-none {
  border: none;
}

.cursor-pointer {
  cursor: pointer;
}
