import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import prettyBytes from 'pretty-bytes';
import { consolidatedFileValidationService } from '../../../services/ConsolidatedFileValidationService.js';
import FileUploadProgress from './FileUploadProgress.jsx';
import UploadErrorBoundary from './UploadErrorBoundary.jsx';
import { securityScanner } from '../../../utils/SecurityScanner.js';
import { useUploadProgress } from '../../../hooks/useUploadProgress.js';
import UploadProgress from '../../../components/features/upload/UploadProgress.jsx';

/**
 * Enhanced Drag & Drop Upload Component
 * Implements comprehensive file upload with validation, progress tracking, and accessibility
 *
 * Features:
 * - React Dropzone integration for enhanced drag & drop
 * - File validation (type, size, count)
 * - Visual feedback for drag states
 * - Progress tracking
 * - Error handling with user-friendly messages
 * - Accessibility compliance (WCAG 2.1 AA)
 * - Responsive design with TailwindCSS 4.0
 */
function DragDropUpload({
  onFilesSelected,
  onUploadProgress,
  onError,
  maxFiles = 10,
  maxSize = 10 * 1024 * 1024, // 10MB
  acceptedTypes = {
    'application/pdf': ['.pdf'],
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png']
  },
  disabled = false,
  className = '',
  showEnhancedProgress = true
}) {
  const [uploadState, setUploadState] = useState({
    isUploading: false,
    progress: 0,
    currentFile: null,
    error: null,
    validationResult: null,
    securityResult: null,
    showValidationDetails: false
  });

  // Enhanced progress tracking using custom hook
  const {
    files: progressFiles,
    overallProgress,
    isActive: isProgressActive,
    totalFiles,
    initializeFiles: initProgressFiles,
    updateFileProgress: updateProgress,
    cancelProcessing,
    retryFiles,
    clearFiles,
    startProcessing: startProgressProcessing
  } = useUploadProgress({
    maxFiles,
    onProgress: onUploadProgress,
    onError: onError
  });

  // Initialize files for progress tracking (wrapper for hook)
  const initializeFiles = useCallback((files) => {
    return initProgressFiles(files);
  }, [initProgressFiles]);

  // Update file progress (wrapper for hook)
  const updateFileProgress = useCallback((fileId, updates) => {
    return updateProgress(fileId, updates);
  }, [updateProgress]);

  // Start processing (wrapper for hook)
  const startProcessing = useCallback(() => {
    return startProgressProcessing();
  }, [startProgressProcessing]);

  // File drop handler with enhanced validation
  const onDrop = useCallback(async (acceptedFiles, rejectedFiles) => {
    // Handle rejected files from react-dropzone
    if (rejectedFiles.length > 0) {
      const errors = rejectedFiles.map(({ file, errors }) => ({
        fileName: file.name,
        errors: errors.map(e => e.message)
      }));

      const errorMessage = `File validation failed:\n${errors.map(e =>
        `• ${e.fileName}: ${e.errors.join(', ')}`
      ).join('\n')}`;

      setUploadState(prev => ({
        ...prev,
        error: errorMessage,
        validationResult: null,
        securityResult: null
      }));
      onError?.(errorMessage);
      return;
    }

    // Clear previous state
    setUploadState(prev => ({
      ...prev,
      error: null,
      validationResult: null,
      securityResult: null
    }));

    // Initialize progress tracking for accepted files
    const progressFiles = initializeFiles(acceptedFiles);

    try {
      // Start processing and update file statuses to validating
      startProcessing();
      progressFiles.forEach(progressFile => {
        updateFileProgress(progressFile.id, {
          status: 'validating',
          stage: 'validating',
          progress: 10
        });
      });

      // Browser-compatible validation using consolidated validation service
      const validationResult = await consolidatedFileValidationService.validateFiles(acceptedFiles, {
        maxFiles,
        maxFileSize: maxSize,
        maxTotalSize: maxFiles * maxSize,
        allowedExtensions: Object.keys(acceptedTypes)
      });

      // Update progress for validation completion
      progressFiles.forEach((progressFile, index) => {
        updateFileProgress(progressFile.id, {
          progress: 40, // Validation complete
          stage: 'validated'
        });
      });

      // Update state with validation result
      setUploadState(prev => ({
        ...prev,
        validationResult
      }));

      if (!validationResult.isValid) {
        const errorMessage = `File validation failed:\n${validationResult.errors.join('\n')}`;
        setUploadState(prev => ({ ...prev, error: errorMessage }));

        // Mark files as failed
        progressFiles.forEach(progressFile => {
          updateFileProgress(progressFile.id, {
            status: 'error',
            error: errorMessage,
            progress: 0
          });
        });

        onError?.(errorMessage);
        return;
      }

      // Update progress for validation completion
      progressFiles.forEach(progressFile => {
        updateFileProgress(progressFile.id, {
          progress: 40,
          validationResult
        });
      });

      // Perform security scanning on valid files
      const securityResults = await Promise.all(
        acceptedFiles.map(async (file, index) => {
          const progressFile = progressFiles[index];
          if (progressFile) {
            updateFileProgress(progressFile.id, {
              stage: 'validating',
              progress: 50
            });
          }

          const result = await securityScanner.scanFile(file);

          if (progressFile) {
            updateFileProgress(progressFile.id, {
              progress: 70,
              securityResult: result
            });
          }

          return result;
        })
      );

      // Check if any files failed security scan
      const insecureFiles = securityResults.filter(result => !result.isSecure);
      if (insecureFiles.length > 0) {
        const securityError = `Security scan failed:\n${insecureFiles.map(result =>
          result.threats.map(threat => `• ${threat.description}`).join('\n')
        ).join('\n')}`;

        setUploadState(prev => ({
          ...prev,
          error: securityError,
          securityResult: securityResults[0] // Show first result for feedback
        }));

        // Mark files as failed
        progressFiles.forEach((progressFile, index) => {
          const securityResult = securityResults[index];
          updateFileProgress(progressFile.id, {
            status: 'error',
            error: securityResult?.isSecure ? null : 'Security scan failed',
            progress: 0
          });
        });

        onError?.(securityError);
        return;
      }

      // Store security results for display
      setUploadState(prev => ({
        ...prev,
        securityResult: securityResults[0] // Show first result for feedback
      }));

      // Update progress for security scan completion
      progressFiles.forEach(progressFile => {
        updateFileProgress(progressFile.id, {
          progress: 80,
          stage: 'processing'
        });
      });

      // Process valid and secure files
      if (acceptedFiles.length > 0) {
        setUploadState(prev => ({
          ...prev,
          isUploading: true,
          progress: 0,
          currentFile: acceptedFiles[0].name
        }));

        try {
          // Update files to processing status
          progressFiles.forEach(progressFile => {
            updateFileProgress(progressFile.id, {
              status: 'processing',
              stage: 'processing',
              progress: 90
            });
          });

          await onFilesSelected?.(acceptedFiles);

          // Mark files as complete
          progressFiles.forEach(progressFile => {
            updateFileProgress(progressFile.id, {
              status: 'complete',
              stage: 'complete',
              progress: 100
            });
          });

          setUploadState(prev => ({
            ...prev,
            isUploading: false,
            progress: 100,
            currentFile: null
          }));

          // Clear success state after 2 seconds
          setTimeout(() => {
            setUploadState(prev => ({ ...prev, progress: 0 }));
          }, 2000);

        } catch (error) {
          // Mark files as failed
          progressFiles.forEach(progressFile => {
            updateFileProgress(progressFile.id, {
              status: 'error',
              error: error.message,
              progress: 0
            });
          });

          setUploadState(prev => ({
            ...prev,
            isUploading: false,
            error: error.message,
            currentFile: null
          }));
          onError?.(error.message);
        }
      }

    } catch (error) {
      // Handle validation/security scanning errors
      const errorMessage = `Validation error: ${error.message}`;

      // Mark all files as failed
      progressFiles.forEach(progressFile => {
        updateFileProgress(progressFile.id, {
          status: 'error',
          error: errorMessage,
          progress: 0
        });
      });

      setUploadState(prev => ({
        ...prev,
        error: errorMessage,
        validationResult: null,
        securityResult: null
      }));
      onError?.(errorMessage);
    }
  }, [onFilesSelected, onError, maxSize, acceptedTypes, maxFiles, initializeFiles, updateFileProgress, startProcessing]);

  // Configure dropzone
  const {
    getRootProps,
    getInputProps,
    isDragActive,
    isDragAccept,
    isDragReject,
    open
  } = useDropzone({
    onDrop,
    accept: acceptedTypes,
    maxFiles,
    maxSize,
    disabled: disabled || uploadState.isUploading,
    noClick: true, // We'll handle click manually for better UX
    noKeyboard: false // Keep keyboard support for accessibility
  });

  // Determine visual state
  const getDropzoneClassName = () => {
    const baseClasses = `
      relative border-2 border-dashed rounded-lg p-8 text-center transition-all duration-200
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
      ${className}
    `;

    if (disabled || uploadState.isUploading) {
      return `${baseClasses} border-gray-200 bg-gray-50 cursor-not-allowed`;
    }

    if (isDragReject || uploadState.error) {
      return `${baseClasses} border-red-300 bg-red-50 text-red-700`;
    }

    if (isDragAccept) {
      return `${baseClasses} border-green-300 bg-green-50 text-green-700`;
    }

    if (isDragActive) {
      return `${baseClasses} border-blue-300 bg-blue-50 text-blue-700`;
    }

    return `${baseClasses} border-gray-300 bg-white hover:border-gray-400 hover:bg-gray-50`;
  };

  // Clear error handler
  const clearError = useCallback(() => {
    setUploadState(prev => ({
      ...prev,
      error: null,
      validationResult: null,
      securityResult: null
    }));
  }, []);

  // Toggle validation details
  const toggleValidationDetails = useCallback(() => {
    setUploadState(prev => ({
      ...prev,
      showValidationDetails: !prev.showValidationDetails
    }));
  }, []);

  // Format accepted file types for display
  const formatAcceptedTypes = () => {
    const extensions = Object.values(acceptedTypes).flat();
    return extensions.join(', ').toUpperCase();
  };

  return (
    <UploadErrorBoundary>
      <div className="w-full">
        {/* Main dropzone */}
        <div
          {...getRootProps()}
          className={getDropzoneClassName()}
          role="button"
          tabIndex={0}
          aria-label={`Upload files. Accepted formats: ${formatAcceptedTypes()}. Maximum size: ${prettyBytes(maxSize)}`}
          aria-describedby="upload-instructions upload-error"
        >
          <input {...getInputProps()} aria-label="File upload input" />

          {uploadState.isUploading ? (
            <FileUploadProgress
              progress={uploadState.progress}
              fileName={uploadState.currentFile}
              stage="uploading"
            />
          ) : (
            <div className="space-y-4">
              {/* Upload icon */}
              <div className="mx-auto w-16 h-16 flex items-center justify-center">
                {isDragActive ? (
                  <div className="text-4xl animate-bounce">📥</div>
                ) : uploadState.error ? (
                  <div className="text-4xl text-red-500">⚠️</div>
                ) : (
                  <div className="text-4xl text-gray-400">📄</div>
                )}
              </div>

              {/* Upload text */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {isDragActive
                    ? 'Drop files here'
                    : 'Drag & drop files here'
                  }
                </h3>
                <p id="upload-instructions" className="text-sm text-gray-600 mb-4">
                  or{' '}
                  <button
                    type="button"
                    onClick={open}
                    disabled={disabled || uploadState.isUploading}
                    className="text-blue-600 hover:text-blue-700 font-medium focus:outline-none focus:underline disabled:text-gray-400"
                    aria-label="Click to select files"
                  >
                    click to select files
                  </button>
                </p>

                {/* File requirements */}
                <div className="text-xs text-gray-500 space-y-1">
                  <p>Supported formats: {formatAcceptedTypes()}</p>
                  <p>Maximum file size: {prettyBytes(maxSize)}</p>
                  <p>Maximum files: {maxFiles}</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Validation Feedback */}
        {uploadState.validationResult && !uploadState.validationResult.isValid && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <span className="text-yellow-500 text-lg">⚠️</span>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-yellow-800 mb-1">
                  File Validation Issues
                </h4>
                <ul className="text-sm text-yellow-700 list-disc list-inside">
                  {uploadState.validationResult.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
                <button
                  type="button"
                  onClick={clearError}
                  className="mt-2 text-xs text-yellow-600 hover:text-yellow-800 focus:outline-none focus:underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Security Scan Feedback */}
        {uploadState.securityResult && !uploadState.securityResult.isSecure && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <span className="text-red-500 text-lg">🛡️</span>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-red-800 mb-1">
                  Security Scan Failed
                </h4>
                <p className="text-sm text-red-700">
                  Risk Level: {uploadState.securityResult.riskLevel} ({uploadState.securityResult.riskScore}%)
                </p>
                <button
                  type="button"
                  onClick={clearError}
                  className="mt-2 text-xs text-red-600 hover:text-red-800 focus:outline-none focus:underline"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Legacy Error display (fallback) */}
        {uploadState.error && !uploadState.validationResult && !uploadState.securityResult && (
          <div
            id="upload-error"
            className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md"
            role="alert"
            aria-live="polite"
          >
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <span className="text-red-500 text-lg" aria-hidden="true">⚠️</span>
              </div>
              <div className="ml-3 flex-1">
                <h4 className="text-sm font-medium text-red-800 mb-1">
                  Upload Error
                </h4>
                <pre className="text-sm text-red-700 whitespace-pre-wrap font-sans">
                  {uploadState.error}
                </pre>
                <button
                  type="button"
                  onClick={clearError}
                  className="mt-2 text-xs text-red-600 hover:text-red-800 focus:outline-none focus:underline"
                  aria-label="Dismiss error message"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Validation Details Toggle */}
        {uploadState.validationResult && (
          <div className="mt-2">
            <button
              type="button"
              onClick={toggleValidationDetails}
              className="text-xs text-blue-600 hover:text-blue-800 focus:outline-none focus:underline"
            >
              {uploadState.showValidationDetails ? 'Hide' : 'Show'} Validation Details
            </button>
          </div>
        )}

        {/* Enhanced Upload Progress */}
        {showEnhancedProgress && (isProgressActive || totalFiles > 0) && (
          <div className="mt-4">
            <UploadProgress
              files={progressFiles}
              overallProgress={overallProgress}
              isActive={isProgressActive}
              onCancel={() => cancelProcessing()}
              onRetry={(fileIds) => retryFiles(fileIds)}
              onClear={() => clearFiles()}
              showDetails={true}
            />
          </div>
        )}

        {/* Success indicator */}
        {uploadState.progress === 100 && !uploadState.isUploading && !uploadState.error && (
          <div
            className="mt-4 p-4 bg-green-50 border border-green-200 rounded-md"
            role="status"
            aria-live="polite"
          >
            <div className="flex items-center">
              <span className="text-green-500 text-lg mr-3" aria-hidden="true">✅</span>
              <p className="text-sm text-green-700 font-medium">
                Files uploaded successfully!
              </p>
            </div>
          </div>
        )}
      </div>
    </UploadErrorBoundary>
  );
}

export default DragDropUpload;
