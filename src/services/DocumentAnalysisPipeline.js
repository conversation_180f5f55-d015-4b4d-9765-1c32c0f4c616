/**
 * Document Analysis Pipeline Service
 * Coordinates multi-step document processing:
 * - PDF text extraction
 * - DeepSeek analysis
 * - Tesseract OCR extraction
 * - Field extraction and validation
 */

import PDFService from './PDFService';
import DeepSeekService from './DeepSeekService';
import TesseractService from './TesseractService';
import { COMMON_FIELDS, DOCUMENT_KIND_FIELDS, FIELD_DESCRIPTIONS } from '../core/config/fieldDefinitions';
import { DOCUMENT_TYPE_PATTERNS, mapToFakturowniaDocumentType } from '../core/config/documentTypes';
import LanguagesMapping from '../core/config/languageMappings';

class DocumentAnalysisPipeline {
  constructor(apiKey) {
    this.pdfService = new PDFService();
    this.deepSeekService = new DeepSeekService(apiKey);
    this.tesseractService = new TesseractService();
    this.languageMapping = new LanguagesMapping();
  }

  /**
   * Process document through multi-step pipeline
   * @param {File|Blob} file - Document file to process
   * @returns {Promise<Object>} - Analysis results with confidence scores
   */
  async processDocument(file) {
    try {
      // Step 1: Extract text from PDF
      console.log('Step 1: Extracting text from PDF...');
      const pdfTextResult = await this.pdfService.extractText(file);

      // Detect document language
      const language = this.languageMapping.detectDocumentLanguageCode(pdfTextResult.text);
      console.log('Detected language:', language);

      // Step 2: DeepSeek analysis on extracted text
      console.log('Step 2: Performing DeepSeek analysis...');
      const deepSeekResult = await this.deepSeekService.analyzeText(pdfTextResult.text);

      // Step 3: Tesseract OCR extraction for structure reference
      console.log('Step 3: Performing OCR analysis...');
      await this.tesseractService.setLanguages(language);
      const ocrResult = await this.tesseractService.extractText(file);

      // Step 4: Field extraction and validation
      console.log('Step 4: Extracting and validating fields...');
      const fields = await this.extractFields(pdfTextResult, deepSeekResult, ocrResult);

      // Store intermediate results and confidence scores
      const analysisResult = {
        document_type: this.detectDocumentType(pdfTextResult.text, deepSeekResult),
        language,
        confidence: {
          overall: this.calculateOverallConfidence(fields),
          fields: this.getFieldConfidenceScores(fields)
        },
        metadata: {
          pdf: pdfTextResult.metadata,
          pages: pdfTextResult.pages,
          processing_time: new Date().toISOString()
        },
        fields,
        raw_results: {
          pdf_text: pdfTextResult.text,
          deepseek: deepSeekResult,
          ocr: ocrResult
        }
      };

      console.log('Document analysis completed successfully');
      return analysisResult;
    } catch (error) {
      console.error('Error in document analysis pipeline:', error);
      throw error;
    }
  }

  /**
   * Extract fields from all analysis results
   * @param {Object} pdfResult - PDF extraction result
   * @param {Object} deepSeekResult - DeepSeek analysis result
   * @param {Object} ocrResult - OCR analysis result
   * @returns {Promise<Object>} Extracted fields with confidence scores
   */
  async extractFields(pdfResult, deepSeekResult, ocrResult) {
    const fields = {};
    const confidenceThreshold = 0.8;

    // Process common fields
    for (const fieldName of COMMON_FIELDS) {
      // Get field values from different sources
      const deepSeekValue = deepSeekResult.fields[fieldName];
      const ocrValue = this.findFieldInOcrText(fieldName, ocrResult);

      if (deepSeekValue && deepSeekValue.confidence >= confidenceThreshold) {
        fields[fieldName] = {
          value: deepSeekValue.value,
          confidence: deepSeekValue.confidence,
          source: 'deepseek'
        };
      } else if (ocrValue && ocrValue.confidence >= confidenceThreshold) {
        fields[fieldName] = {
          value: ocrValue.value,
          confidence: ocrValue.confidence,
          source: 'ocr'
        };
      }
    }

    // Cross-validate fields
    this.crossValidateFields(fields);

    return fields;
  }

  /**
   * Find field value in OCR text using patterns
   * @param {string} fieldName - Field to find
   * @param {Object} ocrResult - OCR analysis result
   * @returns {Object|null} Field value and confidence if found
   */
  findFieldInOcrText(fieldName, ocrResult) {
    const fieldDescription = FIELD_DESCRIPTIONS[fieldName];
    if (!fieldDescription) return null;

    // Search in OCR lines for field patterns
    for (const line of ocrResult.lines) {
      const lowerLine = line.text.toLowerCase();
      const lowerDesc = fieldDescription.toLowerCase();

      if (lowerLine.includes(lowerDesc) || 
          lowerLine.includes(fieldName.toLowerCase())) {
        // Extract value from next line or after the field name
        const nextLine = ocrResult.lines[ocrResult.lines.indexOf(line) + 1];
        if (nextLine) {
          return {
            value: nextLine.text.trim(),
            confidence: nextLine.confidence
          };
        }
      }
    }

    return null;
  }

  /**
   * Cross-validate fields from different sources
   * @param {Object} fields - Extracted fields
   */
  crossValidateFields(fields) {
    for (const [fieldName, field] of Object.entries(fields)) {
      // If field has low confidence, try to validate with other sources
      if (field.confidence < 0.9) {
        // TODO: Implement cross-validation logic
        // For example, compare with known patterns, validate against business rules
      }
    }
  }

  /**
   * Calculate overall confidence score
   * @param {Object} fields - Extracted fields
   * @returns {number} Overall confidence score
   */
  calculateOverallConfidence(fields) {
    const confidences = Object.values(fields).map(f => f.confidence);
    if (confidences.length === 0) return 0;
    return confidences.reduce((a, b) => a + b) / confidences.length;
  }

  /**
   * Get confidence scores for all fields
   * @param {Object} fields - Extracted fields
   * @returns {Object} Field confidence scores
   */
  getFieldConfidenceScores(fields) {
    const scores = {};
    for (const [fieldName, field] of Object.entries(fields)) {
      scores[fieldName] = field.confidence;
    }
    return scores;
  }

  /**
   * Detect document type from content
   * @param {string} text - Document text
   * @param {Object} deepSeekResult - DeepSeek analysis result
   * @returns {string} Detected document type
   */
  detectDocumentType(text, deepSeekResult) {
    // First try DeepSeek's document type detection
    if (deepSeekResult.document_type) {
      return mapToFakturowniaDocumentType(deepSeekResult.document_type, text);
    }

    // Fall back to pattern matching
    const lowerText = text.toLowerCase();
    for (const [docType, patterns] of Object.entries(DOCUMENT_TYPE_PATTERNS)) {
      if (patterns.some(pattern => lowerText.includes(pattern))) {
        return docType;
      }
    }

    return 'vat'; // Default to VAT invoice
  }
}

export default DocumentAnalysisPipeline;
