#===================================================================================================
#=== Makefile Configuration
#===================================================================================================

# Force specific make behavior
.EXPORT_ALL_VARIABLES:
MAKEFLAGS     += --always-make
MAKEFLAGS     += --no-builtin-rules
MAKEFLAGS     += --no-print-directory
MAKEFLAGS     += --warn-undefined-variables
MAKEFLAGS     += -j1

# Shell configuration
.ONESHELL:    # enable multiline shell scripts as targets
.SHELL        := $(shell which bash)
SHELL         := $(shell which bash)
.SHELLFLAGS   := -eu -o pipefail -c
SHELL_NAME    := $(notdir ${SHELL})
SHELL_VERSION := $(shell echo $${BASH_VERSION%%[^0-9.]*})

# Sudo configuration
SUDO          ?= $(shell which sudo || true)
___XHOST___   ?= $(shell xhost +local:docker)

M             ?= $(shell which gmake)

#===================================================================================================
#=== Local / App Configuration
#===================================================================================================

-include .env

AZURE_CONFIG_DIR := $(HOME)/.azure/tenant.d/$(AZ_TENANT_ID)
AZURE_LOGIN_CONFIG := $(AZURE_CONFIG_DIR)

# User/Group configuration
UID                 := $(shell id -u)
GID                 := $(shell id -g)
USERNAME            := app

PROJECT_NAME        ?= mrent
DOMAIN              ?= mrent.app

VERSION             := $(shell cat VERSION || echo "0.1.0")


#===================================================================================================
#=== Makefile Configuration
#===================================================================================================

CONDA_ENV ?= py313_accounting-chrome-extension
CONDA_PREFIX ?= $(HOME)/miniconda3/envs/$(CONDA_ENV)

# Node configuration
NVM_NODE_VERSION ?= lts/jod

NVM_DIR := ${HOME}/.nvm
#===================================================================================================
#=== Makefile Targets
#===================================================================================================

# Build the Chrome extension for production
build-extension:
	@echo "🔨 Building Chrome extension for production..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@echo "📋 Node.js version: $$(node --version)"
	@echo "📋 NPM version: $$(npm --version)"
	@echo "📋 Extension version: $(VERSION)"
	@echo "🧹 Cleaning previous build..."
	@rm -rf dist/build/
	@mkdir -p dist/build
	@echo "📦 Installing dependencies..."
	@npm install --legacy-peer-deps
	@echo "🔨 Building with vite for production..."
	@VITE_BUILD_TARGET=build npm run build
	@echo "✅ Chrome extension built successfully!"
	@echo "📁 Built files are in the dist/build/ directory"
	@echo "🔍 Verifying build output..."
	@ls -la dist/build/
	@echo "🎉 Production build complete! Load the extension from dist/build/ directory in Chrome."

# Development build with separate directory
dev-extension:
	@echo "🔨 Building Chrome extension for development..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@echo "📋 Node.js version: $$(node --version)"
	@echo "📋 NPM version: $$(npm --version)"
	@echo "📋 Extension version: $(VERSION)"
	@echo "🧹 Cleaning previous development build..."
	@rm -rf dist/dev/
	@mkdir -p dist/dev
	@echo "📦 Installing dependencies..."
	@npm install --legacy-peer-deps
	@echo "🔨 Building with vite for development..."
	@VITE_BUILD_TARGET=dev npm run dev:build
	@echo "✅ Chrome extension development build completed!"

# Install Chrome 135 for selenium testing (compatible with --load-extension)
install-chrome-135:
	@echo "🔧 Installing Chrome 135 for selenium testing..."
	@mkdir -p tests/selenium/chrome-135
	@if [ ! -f "tests/selenium/chrome-135/chrome-linux64.zip" ]; then \
		echo "📥 Downloading Chrome 135.0.7049.97..."; \
		cd tests/selenium/chrome-135 && wget -q https://storage.googleapis.com/chrome-for-testing-public/135.0.7049.97/linux64/chrome-linux64.zip; \
	fi
	@if [ ! -d "tests/selenium/chrome-135/chrome-linux64" ]; then \
		echo "📦 Extracting Chrome 135..."; \
		cd tests/selenium/chrome-135 && unzip -q chrome-linux64.zip; \
	fi
	@echo "📦 Installing Chrome 135 dependencies..."
	@sudo apt-get update -qq && sudo apt-get install -y -qq \
		libnss3 libatk-bridge2.0-0 libdrm2 libxcomposite1 libxdamage1 libxrandr2 \
		libgbm1 libxss1 libasound2 libatspi2.0-0 libgtk-3-0 libgdk-pixbuf2.0-0 \
		libxshmfence1 || true
	@echo "✅ Chrome 135 installed at tests/selenium/chrome-135/chrome-linux64/chrome"
	@echo "💡 This Chrome 135 supports --load-extension flag for selenium testing"
	@echo "📁 Built files are in the dist/dev/ directory"
	@echo "🔍 Verifying build output..."
	@ls -la dist/dev/
	@echo "🎉 Development build complete! Load the extension from dist/dev/ directory in Chrome."

# Development build with watch mode
dev-extension-watch:
	@echo "🔨 Starting development build with watch mode..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@echo "📋 Node.js version: $$(node --version)"
	@echo "📋 NPM version: $$(npm --version)"
	@echo "📋 Extension version: $(VERSION)"
	@echo "🧹 Cleaning previous development build..."
	@rm -rf dist/dev/
	@mkdir -p dist/dev
	@echo "📦 Installing dependencies..."
	@npm install --legacy-peer-deps
	@echo "👀 Starting vite in watch mode for development..."
	@VITE_BUILD_TARGET=dev npm run dev

# Clean build artifacts
clean-extension:
	@echo "🧹 Cleaning extension build artifacts..."
	@rm -rf dist/
	@rm -rf node_modules/.cache/
	@echo "✅ Clean complete!"

# Clean only development build
clean-dev:
	@echo "🧹 Cleaning development build artifacts..."
	@rm -rf dist/dev/
	@echo "✅ Development clean complete!"

# Clean only production build
clean-build:
	@echo "🧹 Cleaning production build artifacts..."
	@rm -rf dist/build/
	@echo "✅ Production clean complete!"

# Enhanced Testing Framework
test-all: test-unit test-functional test-e2e test-visual test-selenium
	@echo "🎉 ALL TESTS COMPLETED SUCCESSFULLY!"

# Selenium browser tests for Chrome extension state verification
test-selenium:
	@echo "🌐 Running Selenium browser tests..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@if [ ! -d "dist" ]; then \
		echo "⚠️  Extension not built. Building now..."; \
		make build-extension; \
	fi
	@cd tests/selenium && python extension_state_tests.py
	@echo "✅ Selenium tests completed!"

# Quick selenium test for assignment workflow
selenium-verify:
	@echo "🔍 Quick Selenium verification for assignment workflow..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@if [ ! -d "dist" ]; then \
		echo "⚠️  Extension not built. Building now..."; \
		make build-extension; \
	fi
	@cd tests/selenium && python extension_state_tests.py
	@echo "✅ Selenium verification completed!"

# OCR integration tests
test-ocr-integration:
	@echo "🧪 Testing OCR integration files..."
	@echo "📁 Checking OCR service files..."
	@test -f src/services/OCRProcessingService.js && echo "✅ OCRProcessingService.js exists" || echo "❌ OCRProcessingService.js missing"
	@test -f src/utils/imageUtils.js && echo "✅ imageUtils.js exists" || echo "❌ imageUtils.js missing"
	@test -f src/utils/ocrUtils.js && echo "✅ ocrUtils.js exists" || echo "❌ ocrUtils.js missing"
	@echo "📊 File sizes:"
	@ls -la src/services/OCRProcessingService.js src/utils/imageUtils.js src/utils/ocrUtils.js 2>/dev/null || echo "Some files missing"
	@echo "✅ OCR integration files check completed"

# Unit tests with Jest and React Testing Library
test-unit:
	@echo "🧪 Running unit tests with Vitest..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npm run test:unit
	@echo "✅ Unit tests completed!"

# Functional tests (enhanced existing + new API tests)
test-functional: functional-tests-src test-api-integration
	@echo "✅ Functional tests completed!"

# E2E tests with Playwright
test-e2e:
	@echo "🎭 Running E2E tests with Playwright..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npm run build
	@npx playwright test --project=chrome-extension
	@echo "✅ E2E tests completed!"

# Visual regression tests with Selenium
test-visual:
	@echo "📸 Running visual regression tests with Selenium..."
	@if [ ! -d "tests/visual/venv" ]; then \
		echo "⚠️  Python virtual environment not found. Setting up..."; \
		make setup-python-venv; \
	fi
	@source tests/visual/venv/bin/activate && python tests/visual/visual_test_runner.py
	@echo "✅ Visual tests completed!"

# Watch mode for development
test-watch:
	@echo "👀 Starting test watch mode..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npm run test:unit -- --watch

# Test coverage reporting
test-coverage:
	@echo "📊 Generating test coverage report..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npm run test:coverage
	@echo "📋 Coverage report generated in coverage/ directory"

# API integration tests
test-api-integration:
	@echo "🔌 Running API integration tests..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@node tests/functional/api/apiTestSuite.js
	@echo "✅ API integration tests completed!"

# Performance tests
test-performance:
	@echo "⚡ Running performance tests..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@node tests/performance/performanceTests.js
	@echo "✅ Performance tests completed!"

# Version bumping
bump-version:
	@echo "📈 Bumping version..."
	@current_version=$$(cat VERSION)
	@echo "Current version: $$current_version"
	@IFS='.' read -r major minor patch <<< "$$current_version"
	@new_patch=$$((patch + 1))
	@new_version="$$major.$$minor.$$new_patch"
	@echo "$$new_version" > VERSION
	@echo "✅ Version bumped from $$current_version to $$new_version"

# Pre-commit testing pipeline with version bump
pre-commit: lint test-unit test-functional selenium-verify bump-version
	@echo "✅ Pre-commit checks passed - ready to commit!"

# CI testing pipeline
test-ci: test-all test-performance
	@echo "🚀 CI testing pipeline completed successfully!"

# Test and build pipeline
test-and-build: test-all build-extension
	@echo "🎉 Test and build pipeline completed!"

# Test multi-step pipeline implementation
test-pipeline:
	@echo "🧪 Testing multi-step pipeline implementation..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@node test-pipeline-core.js
	@echo "✅ Pipeline testing completed!"

# Test document processing functionality
test-functional-pipeline:
	@echo "🧪 Testing document processing functionality..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@node test-document-processing-functional.js
	@echo "✅ Functional testing completed!"

# Legacy functional tests (enhanced)
functional-tests-src:
	@echo "🧪 Starting enhanced functional tests for all JavaScript files..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@echo "📋 Node.js version: $$(node --version)"
	@echo "📋 NPM version: $$(npm --version)"
	@echo ""
	@total_files=0
	@passed_files=0
	@failed_files=0
	@empty_files=0
	@failed_list=""
	@empty_list=""
	@test_failed=0
	@for x in $$(find src -type f -name "*.js" | sort); do
		echo ">>> 🧪 Testing $$x"
		total_files=$$((total_files + 1))
		if output=$$(timeout 30s node "$$x" 2>&1); then
			exit_code=$$?
			if [ -z "$$output" ] || ! echo "$$output" | grep -q "🧪\|✅\|Test\|test"; then
				echo "⚠️  EMPTY: $$x (no test output detected)"
				empty_files=$$((empty_files + 1))
				empty_list="$$empty_list $$x"
				echo "❌ STOPPING: No tests found in $$x" >&2
				test_failed=1
				break
			elif echo "$$output" | grep -qE "(Error:|ERROR:|Exception:|ReferenceError|TypeError|SyntaxError|❌.*Error|❌.*Failed|Test.*FAILED|Tests.*FAILED)" && ! echo "$$output" | grep -q "✅.*Testing Summary"; then
				echo "❌ FAILED: $$x (errors detected in output)"
				echo "📝 Error output:"
				echo "$$output" | grep -E "(Error|error:|ERROR|Exception|FAIL|fail:|Failed|failed:|ReferenceError|TypeError|SyntaxError)" | head -5
				failed_files=$$((failed_files + 1))
				failed_list="$$failed_list $$x"
				echo "❌ STOPPING: Errors found in $$x" >&2
				test_failed=1
				break
			else
				echo "✅ PASSED: $$x"
				passed_files=$$((passed_files + 1))
			fi
		else
			exit_code=$$?
			if [ $$exit_code -eq 124 ]; then
				echo "⏰ TIMEOUT: $$x (exceeded 30 seconds)"
				failed_files=$$((failed_files + 1))
				failed_list="$$failed_list $$x"
				echo "❌ STOPPING: Test timeout in $$x" >&2
				test_failed=1
				break
			else
				echo "❌ FAILED: $$x (exit code: $$exit_code)"
				echo "📝 Error output:"
				echo "$$output" | head -10
				failed_files=$$((failed_files + 1))
				failed_list="$$failed_list $$x"
				echo "❌ STOPPING: Test failed in $$x" >&2
				test_failed=1
				break
			fi
		fi
		echo ""
	done
	@if [ $$test_failed -eq 0 ]; then
		echo "🎉 LEGACY FUNCTIONAL TESTS COMPLETED SUCCESSFULLY!"
		echo "📊 Summary: $$total_files total, $$passed_files passed, $$failed_files failed, $$empty_files empty"
	else
		echo "❌ TESTS FAILED - See errors above"
		exit 1
	fi


# Development setup and dependencies
install-deps:
	@echo "🐍 Setting up Python virtual environment for visual testing..."
	@python3 -m venv tests/visual/venv
	@source tests/visual/venv/bin/activate && pip install --upgrade pip setuptools wheel
	@source tests/visual/venv/bin/activate && pip install -r tests/visual/requirements.txt
	@echo "📦 Installing Node.js dependencies..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npm install
	@echo "✅ All dependencies installed!"

# Install additional package
install-package:
	@echo "📦 Installing additional package: $(PACKAGE)..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npm install $(PACKAGE)
	@echo "✅ Package $(PACKAGE) installed!"

# Setup Python virtual environment only
setup-python-venv:
	@echo "🐍 Setting up Python virtual environment..."
	@python3 -m venv tests/visual/venv
	@source tests/visual/venv/bin/activate && pip install --upgrade pip setuptools wheel
	@source tests/visual/venv/bin/activate && pip install -r tests/visual/requirements.txt
	@echo "✅ Python virtual environment setup complete!"
	@echo "💡 To activate: source tests/visual/venv/bin/activate"

# Setup development environment
setup-dev: install-deps
	@echo "🔧 Setting up development environment..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npx husky install
	@npx playwright install --with-deps
	@echo "✅ Development environment setup complete!"

# Code quality and linting
lint:
	@echo "🔍 Running ESLint..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npx eslint src/ tests/ --ext .js,.jsx
	@echo "✅ Linting completed!"

lint-fix:
	@echo "🔧 Running ESLint with auto-fix..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@npx eslint src/ tests/ --ext .js,.jsx --fix
	@npx prettier --write "src/**/*.{js,jsx,css,md}" "tests/**/*.{js,jsx}"
	@echo "✅ Code formatting completed!"

# Validate extension package
validate-extension:
	@echo "🔍 Validating extension package..."
	@if [ ! -f dist/manifest.json ]; then
		echo "❌ manifest.json not found in dist/"
		exit 1
	fi
	@if [ ! -f dist/popup.html ]; then
		echo "❌ popup.html not found in dist/"
		exit 1
	fi
	@echo "✅ Extension package validation passed!"

functional-tests-src-raw:
	@echo "🧪 Starting functional tests for all JavaScript files..."
	@source ${NVM_DIR}/nvm.sh
	@nvm use ${NVM_NODE_VERSION} || nvm install ${NVM_NODE_VERSION}
	@echo "📋 Node.js version: $$(node --version)"
	@echo "📋 NPM version: $$(npm --version)"
	@echo ""
	@for x in $$(find src -type f -name "*.js" | sort); do
		echo ">>> 🧪 Testing $$x"
		node $${x}
	@done

tree:
	tree  . -I node_modules -I *.pyc -I *.py -I site-packages -I *.png -I venv -I dist
