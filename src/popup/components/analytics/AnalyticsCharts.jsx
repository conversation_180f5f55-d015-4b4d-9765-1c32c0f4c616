/**
 * Analytics Charts Component
 * 
 * Comprehensive chart visualizations for the analytics dashboard
 * using Recharts library for modern, responsive charts.
 */

import React from 'react';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import { 
  formatNumber, 
  formatPercentage, 
  formatDateLabel,
  CHART_COLORS,
  createLineChartConfig,
  createBarChartConfig,
  createPieChartConfig,
  getSeriesColor
} from '../../../utils/chartUtils.js';

/**
 * Document Processing Trend Chart
 */
export const DocumentProcessingTrendChart = ({ data, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Processing Trend</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const config = createLineChartConfig();

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Processing Trend</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} {...config}>
            <CartesianGrid strokeDasharray="3 3" stroke={CHART_COLORS.muted} opacity={0.3} />
            <XAxis 
              dataKey="date" 
              tickFormatter={(date) => formatDateLabel(date, 'short')}
              stroke={CHART_COLORS.muted}
            />
            <YAxis stroke={CHART_COLORS.muted} />
            <Tooltip 
              labelFormatter={(date) => formatDateLabel(date, 'full')}
              formatter={(value, name) => [formatNumber(value), name]}
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="successful" 
              stroke={CHART_COLORS.success} 
              strokeWidth={2}
              name="Successful"
              dot={{ r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="failed" 
              stroke={CHART_COLORS.danger} 
              strokeWidth={2}
              name="Failed"
              dot={{ r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="total" 
              stroke={CHART_COLORS.primary} 
              strokeWidth={2}
              name="Total"
              dot={{ r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * AI Performance Chart
 */
export const AIPerformanceChart = ({ data, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Performance Trend</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const config = createLineChartConfig();

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">AI Performance Trend</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} {...config}>
            <CartesianGrid strokeDasharray="3 3" stroke={CHART_COLORS.muted} opacity={0.3} />
            <XAxis 
              dataKey="date" 
              tickFormatter={(date) => formatDateLabel(date, 'short')}
              stroke={CHART_COLORS.muted}
            />
            <YAxis stroke={CHART_COLORS.muted} />
            <Tooltip 
              labelFormatter={(date) => formatDateLabel(date, 'full')}
              formatter={(value, name) => {
                if (name.includes('Confidence') || name.includes('Rate')) {
                  return [formatPercentage(value), name];
                }
                return [formatNumber(value), name];
              }}
            />
            <Legend />
            <Line 
              type="monotone" 
              dataKey="averageConfidence" 
              stroke={CHART_COLORS.secondary} 
              strokeWidth={2}
              name="Avg Confidence (%)"
              dot={{ r: 4 }}
            />
            <Line 
              type="monotone" 
              dataKey="successRate" 
              stroke={CHART_COLORS.primary} 
              strokeWidth={2}
              name="Success Rate (%)"
              dot={{ r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Business Value Trend Chart
 */
export const BusinessValueTrendChart = ({ data, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Value Trend</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const config = createBarChartConfig();

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Value Trend</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} {...config}>
            <CartesianGrid strokeDasharray="3 3" stroke={CHART_COLORS.muted} opacity={0.3} />
            <XAxis 
              dataKey="month" 
              tickFormatter={(month) => formatDateLabel(month + '-01', 'month')}
              stroke={CHART_COLORS.muted}
            />
            <YAxis 
              stroke={CHART_COLORS.muted}
              tickFormatter={(value) => formatNumber(value, { currency: true, compact: true })}
            />
            <Tooltip 
              labelFormatter={(month) => formatDateLabel(month + '-01', 'month')}
              formatter={(value, name) => [formatNumber(value, { currency: true }), name]}
            />
            <Legend />
            <Bar 
              dataKey="value" 
              fill={CHART_COLORS.accent}
              name="Total Value"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Document Type Distribution Chart
 */
export const DocumentTypeChart = ({ data, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Types</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const config = createPieChartConfig();

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Types</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart {...config}>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              outerRadius={80}
              dataKey="count"
              nameKey="type"
              label={({ type, percent }) => `${type} (${(percent * 100).toFixed(0)}%)`}
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={getSeriesColor(index, data.length)} />
              ))}
            </Pie>
            <Tooltip formatter={(value, name) => [formatNumber(value), name]} />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Top Vendors Chart
 */
export const TopVendorsChart = ({ data, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Vendors</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const config = createBarChartConfig();

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Vendors by Value</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} layout="horizontal" {...config}>
            <CartesianGrid strokeDasharray="3 3" stroke={CHART_COLORS.muted} opacity={0.3} />
            <XAxis 
              type="number"
              stroke={CHART_COLORS.muted}
              tickFormatter={(value) => formatNumber(value, { currency: true, compact: true })}
            />
            <YAxis 
              type="category"
              dataKey="vendor" 
              stroke={CHART_COLORS.muted}
              width={100}
            />
            <Tooltip 
              formatter={(value) => [formatNumber(value, { currency: true }), 'Total Value']}
            />
            <Bar 
              dataKey="value" 
              fill={CHART_COLORS.secondary}
              radius={[0, 4, 4, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Feature Usage Chart
 */
export const FeatureUsageChart = ({ data, className = '' }) => {
  if (!data || data.length === 0) {
    return (
      <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Feature Usage</h3>
        <div className="h-64 flex items-center justify-center text-gray-500">
          No data available
        </div>
      </div>
    );
  }

  const config = createBarChartConfig();

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Feature Usage</h3>
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} {...config}>
            <CartesianGrid strokeDasharray="3 3" stroke={CHART_COLORS.muted} opacity={0.3} />
            <XAxis 
              dataKey="feature" 
              stroke={CHART_COLORS.muted}
              angle={-45}
              textAnchor="end"
              height={80}
            />
            <YAxis stroke={CHART_COLORS.muted} />
            <Tooltip 
              formatter={(value) => [formatNumber(value), 'Usage Count']}
            />
            <Bar 
              dataKey="count" 
              fill={CHART_COLORS.info}
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

/**
 * Combined charts component
 */
const AnalyticsCharts = ({ chartData, className = '' }) => {
  if (!chartData) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="text-center text-gray-500 py-8">
          Loading charts...
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Row 1: Main trend charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <DocumentProcessingTrendChart data={chartData.documentProcessingTrend} />
        <AIPerformanceChart data={chartData.aiPerformanceTrend} />
      </div>

      {/* Row 2: Business value and distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <BusinessValueTrendChart data={chartData.businessValueTrend} />
        <DocumentTypeChart data={chartData.documentTypeDistribution} />
      </div>

      {/* Row 3: Vendors and features */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <TopVendorsChart data={chartData.vendorDistribution} />
        <FeatureUsageChart data={chartData.featureUsageChart} />
      </div>
    </div>
  );
};

export default AnalyticsCharts;
