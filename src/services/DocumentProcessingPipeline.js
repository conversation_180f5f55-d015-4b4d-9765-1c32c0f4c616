/**
 * DocumentProcessingPipeline - Multi-step document analysis pipeline
 * Implements comprehensive document processing through multiple stages:
 * 1. PDF Text Extraction (PDF.js)
 * 2. DeepSeek AI Analysis (Basic fields + metadata)
 * 3. Tesseract OCR Reference (Structural validation)
 * 4. Field Mapping (languageMappings.js + documentTypes.js)
 * 5. Data Validation (fieldDefinitions.js)
 * 6. Final Structured Output
 */

import { processingLogger } from '../utils/ProcessingLogger.js';
import { PDFProcessingService } from './PDFProcessingService.js';
import { sandboxCommunicationService } from './SandboxCommunicationService.js';
import { enhancedDeepSeekAnalysis } from './EnhancedDeepSeekAnalysis.js';
import LanguagesMapping from '../core/config/languageMappings.js';
import {
  guessDocumentType,
  mapToFakturowniaDocumentType,
  getRequiredFieldsForDocumentType
} from '../core/config/documentTypes.js';

export class DocumentProcessingPipeline {
  constructor() {
    this.initialized = false;
    this.pdfService = new PDFProcessingService();
    this.sandboxService = sandboxCommunicationService;
    this.languageMapping = new LanguagesMapping();

    // Pipeline step storage
    this.stepResults = new Map();
    this.stepTimings = new Map();
  }

  /**
   * Initialize the pipeline and all required services
   */
  async initialize() {
    if (this.initialized) return;

    try {
      console.log('🚀 Initializing DocumentProcessingPipeline...');

      // Initialize PDF service
      await this.pdfService.initialize();

      // Initialize sandbox service for OCR
      await this.sandboxService.initialize();
      await this.sandboxService.initializeTesseract('pol+eng');

      this.initialized = true;
      console.log('✅ DocumentProcessingPipeline initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize DocumentProcessingPipeline:', error);
      throw error;
    }
  }

  /**
   * Process document through complete multi-step pipeline
   * @param {File} file - Document file to process
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Complete pipeline results
   */
  async processDocument(file, options = {}) {
    const uploadId = processingLogger.generateUploadId();
    const startTime = performance.now();

    try {
      await this.initialize();

      const {
        progressCallback = null,
        apiKey = null,
        language = 'pol',
        companyInfo = null
      } = options;

      // Clear previous results
      this.stepResults.clear();
      this.stepTimings.clear();

      console.log('🔄 Starting multi-step document processing pipeline...');
      processingLogger.logProcessingStart(uploadId, file.name, file.type, file.size);

      // Step 1: PDF Text Extraction
      if (progressCallback) progressCallback({ stage: 'pdf_extraction', progress: 10 });
      const pdfResult = await this.extractPdfText(file, uploadId, progressCallback);
      this.storeStepResult('pdf_extraction', pdfResult);

      // Step 2: DeepSeek AI Analysis
      if (progressCallback) progressCallback({ stage: 'deepseek_analysis', progress: 30 });
      const deepSeekResult = await this.analyzeWithDeepSeek(pdfResult, uploadId, apiKey, language, companyInfo);
      this.storeStepResult('deepseek_analysis', deepSeekResult);

      // Step 3: Tesseract OCR Reference
      if (progressCallback) progressCallback({ stage: 'tesseract_reference', progress: 50 });
      const ocrResult = await this.extractOcrReference(file, uploadId, progressCallback);
      this.storeStepResult('tesseract_reference', ocrResult);

      // Step 4: Field Mapping
      if (progressCallback) progressCallback({ stage: 'field_mapping', progress: 70 });
      const mappingResult = await this.mapFields(pdfResult, deepSeekResult, ocrResult, uploadId);
      this.storeStepResult('field_mapping', mappingResult);

      // Step 5: Data Validation
      if (progressCallback) progressCallback({ stage: 'data_validation', progress: 85 });
      const validationResult = await this.validateData(mappingResult, uploadId);
      this.storeStepResult('data_validation', validationResult);

      // Step 6: Final Output Generation
      if (progressCallback) progressCallback({ stage: 'final_output', progress: 95 });
      const finalResult = await this.generateFinalOutput(file, uploadId, startTime);
      this.storeStepResult('final_output', finalResult);

      if (progressCallback) progressCallback({ stage: 'complete', progress: 100 });

      const totalTime = performance.now() - startTime;
      console.log(`✅ Multi-step pipeline completed in ${totalTime.toFixed(2)}ms`);

      processingLogger.logProcessingComplete(uploadId, finalResult, totalTime);

      return {
        success: true,
        uploadId,
        processingTimeMs: totalTime,
        stepResults: Object.fromEntries(this.stepResults),
        stepTimings: Object.fromEntries(this.stepTimings),
        finalResult
      };

    } catch (error) {
      console.error('❌ Multi-step pipeline failed:', error);
      processingLogger.logProcessingError(uploadId, 'pipeline_processing', error);

      return {
        success: false,
        uploadId,
        error: error.message,
        stepResults: Object.fromEntries(this.stepResults),
        stepTimings: Object.fromEntries(this.stepTimings)
      };
    }
  }

  /**
   * Step 1: Extract text from PDF using PDF.js
   */
  async extractPdfText(file, uploadId, progressCallback) {
    const stepStart = performance.now();

    try {
      console.log('📄 Step 1: PDF Text Extraction');

      const result = await this.pdfService.extractText(file, {
        onProgress: progressCallback,
        trackingId: uploadId,
        enableOCRFallback: false // We'll do OCR separately
      });

      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('pdf_extraction', stepTime);

      console.log(`✅ PDF text extracted: ${result.text.length} characters in ${stepTime.toFixed(2)}ms`);

      return {
        text: result.text,
        pages: result.pages || 1,
        metadata: result.metadata || {},
        confidence: 100,
        method: 'pdf_text'
      };

    } catch (error) {
      console.error('❌ PDF text extraction failed:', error);
      throw new Error(`PDF text extraction failed: ${error.message}`);
    }
  }

  /**
   * Step 2: Analyze document with DeepSeek AI
   */
  async analyzeWithDeepSeek(pdfResult, uploadId, apiKey, language, companyInfo) {
    const stepStart = performance.now();

    try {
      console.log('🤖 Step 2: DeepSeek AI Analysis');

      if (!apiKey || !pdfResult.text || pdfResult.text.trim().length < 50) {
        console.log('⚠️ Skipping DeepSeek analysis (no API key or insufficient text)');
        return {
          success: false,
          reason: 'No API key or insufficient text',
          metadata: {},
          confidence: 0
        };
      }

      const analysis = await enhancedDeepSeekAnalysis.performComprehensiveAnalysis(
        pdfResult.text,
        apiKey,
        uploadId,
        { language, companyInfo }
      );

      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('deepseek_analysis', stepTime);

      console.log(`✅ DeepSeek analysis completed in ${stepTime.toFixed(2)}ms`);

      return {
        success: true,
        analysis,
        confidence: analysis.confidenceScore?.overall || 0,
        extractedFields: this.extractFieldsFromAnalysis(analysis)
      };

    } catch (error) {
      console.error('❌ DeepSeek analysis failed:', error);
      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('deepseek_analysis', stepTime);

      return {
        success: false,
        error: error.message,
        confidence: 0
      };
    }
  }

  /**
   * Step 3: Extract OCR reference using Tesseract
   */
  async extractOcrReference(file, uploadId, progressCallback) {
    const stepStart = performance.now();

    try {
      console.log('👁️ Step 3: Tesseract OCR Reference');

      // Convert PDF first page to image for OCR
      const imageData = await this.convertPdfToImage(file);

      if (!imageData) {
        console.log('⚠️ Could not convert PDF to image for OCR');
        return {
          success: false,
          reason: 'PDF to image conversion failed',
          text: '',
          confidence: 0
        };
      }

      const ocrResult = await this.sandboxService.processOCR(imageData, {
        language: 'pol+eng',
        confidence: 60
      });

      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('tesseract_reference', stepTime);

      console.log(`✅ OCR reference completed: ${ocrResult.text.length} characters in ${stepTime.toFixed(2)}ms`);

      return {
        success: true,
        text: ocrResult.text,
        confidence: ocrResult.confidence || 0,
        method: 'tesseract_ocr'
      };

    } catch (error) {
      console.error('❌ OCR reference failed:', error);
      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('tesseract_reference', stepTime);

      return {
        success: false,
        error: error.message,
        text: '',
        confidence: 0
      };
    }
  }

  /**
   * Store step result with metadata
   */
  storeStepResult(stepName, result) {
    this.stepResults.set(stepName, {
      ...result,
      timestamp: new Date().toISOString(),
      stepName
    });
  }

  /**
   * Extract structured fields from DeepSeek analysis
   */
  extractFieldsFromAnalysis(analysis) {
    if (!analysis || !analysis.metadata) return {};

    const fields = {};

    // Extract company information
    if (analysis.metadata.companyRelationships) {
      fields.seller_name = analysis.metadata.companyRelationships.vendor || 'Unknown Seller';
      fields.buyer_name = analysis.metadata.companyRelationships.customer || 'Unknown Buyer';
    }

    // Extract transaction patterns
    if (analysis.metadata.transactionPatterns) {
      const amounts = analysis.metadata.transactionPatterns.amounts || {};
      fields.total_net = amounts.net || 0;
      fields.total_vat = amounts.tax || 0;
      fields.total_gross = amounts.gross || 0;
      fields.currency = analysis.metadata.transactionPatterns.currency || 'PLN';
    }

    // Extract document metadata
    if (analysis.metadata.documentMetadata) {
      fields.invoice_number = analysis.metadata.documentMetadata.invoiceNumber || '';
      fields.invoice_date = analysis.metadata.documentMetadata.invoiceDate || '';
    }

    return fields;
  }

  /**
   * Step 4: Map fields using configuration files
   */
  async mapFields(pdfResult, deepSeekResult, ocrResult, uploadId) {
    const stepStart = performance.now();

    try {
      console.log('🗺️ Step 4: Field Mapping');

      const mappedFields = {};

      // Detect document language
      const detectedLanguage = this.languageMapping.detectDocumentLanguageCode(pdfResult.text);
      mappedFields.detected_language = detectedLanguage;

      // Detect document type
      const documentType = await guessDocumentType(pdfResult.text, {
        useAI: false // Use pattern matching for now
      });
      mappedFields.document_type = mapToFakturowniaDocumentType(documentType, pdfResult.text);

      // Map fields from DeepSeek analysis if available
      if (deepSeekResult.success && deepSeekResult.extractedFields) {
        Object.assign(mappedFields, deepSeekResult.extractedFields);
      }

      // Cross-validate with OCR if available
      if (ocrResult.success && ocrResult.text) {
        mappedFields.ocr_validation = this.crossValidateWithOCR(
          mappedFields,
          ocrResult.text
        );
      }

      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('field_mapping', stepTime);

      console.log(`✅ Field mapping completed in ${stepTime.toFixed(2)}ms`);

      return {
        success: true,
        mappedFields,
        detectedLanguage,
        documentType: mappedFields.document_type
      };

    } catch (error) {
      console.error('❌ Field mapping failed:', error);
      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('field_mapping', stepTime);

      return {
        success: false,
        error: error.message,
        mappedFields: {}
      };
    }
  }

  /**
   * Step 5: Validate data using field definitions
   */
  async validateData(mappingResult, uploadId) {
    const stepStart = performance.now();

    try {
      console.log('✅ Step 5: Data Validation');

      if (!mappingResult.success) {
        return {
          success: false,
          reason: 'Mapping step failed',
          validatedFields: {},
          validationErrors: ['Field mapping step failed']
        };
      }

      const { mappedFields, documentType } = mappingResult;
      const validationErrors = [];
      const validatedFields = { ...mappedFields };

      // Get required fields for document type
      const requiredFields = getRequiredFieldsForDocumentType(documentType);

      // Check required fields
      for (const field of requiredFields) {
        if (!validatedFields[field] || validatedFields[field] === '') {
          validationErrors.push(`Missing required field: ${field}`);
        }
      }

      // Validate field formats
      this.validateFieldFormats(validatedFields, validationErrors);

      // Calculate accuracy score
      const accuracyScore = this.calculateAccuracyScore(validatedFields);

      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('data_validation', stepTime);

      console.log(`✅ Data validation completed in ${stepTime.toFixed(2)}ms`);
      console.log(`📊 Accuracy score: ${accuracyScore}%`);

      return {
        success: true,
        validatedFields,
        validationErrors,
        accuracyScore,
        isValid: validationErrors.length === 0
      };

    } catch (error) {
      console.error('❌ Data validation failed:', error);
      const stepTime = performance.now() - stepStart;
      this.stepTimings.set('data_validation', stepTime);

      return {
        success: false,
        error: error.message,
        validatedFields: {},
        validationErrors: [error.message]
      };
    }
  }

  /**
   * Step 6: Generate final structured output
   */
  async generateFinalOutput(file, uploadId, startTime) {
    try {
      console.log('🎯 Step 6: Final Output Generation');

      const totalTime = performance.now() - startTime;
      const allResults = Object.fromEntries(this.stepResults);

      // Get the final validated data
      const validationResult = allResults.data_validation || {};
      const mappingResult = allResults.field_mapping || {};
      const pdfResult = allResults.pdf_extraction || {};
      const deepSeekResult = allResults.deepseek_analysis || {};

      const finalOutput = {
        // Document metadata
        id: `DOC-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        uploadId,
        filename: file.name,
        fileSize: file.size,
        fileType: file.type,
        processedAt: new Date().toISOString(),
        processingTimeMs: totalTime,

        // Processing method
        extractionMethod: 'multi_step_pipeline',
        pipelineVersion: '1.0.0',

        // Extracted content
        extractedText: pdfResult.text || '',
        textLength: pdfResult.text?.length || 0,

        // Analysis results
        documentType: mappingResult.documentType || 'unknown',
        detectedLanguage: mappingResult.detectedLanguage || 'pl',

        // Structured data
        ...validationResult.validatedFields,

        // Quality metrics
        confidence: validationResult.accuracyScore || 0,
        accuracyScore: validationResult.accuracyScore || 0,
        validationErrors: validationResult.validationErrors || [],
        isValid: validationResult.isValid || false,

        // Pipeline metadata
        pipelineSteps: Object.keys(allResults),
        stepTimings: Object.fromEntries(this.stepTimings),

        // AI enhancement flags
        aiEnhanced: deepSeekResult.success || false,
        ocrValidated: allResults.tesseract_reference?.success || false
      };

      console.log(`🎯 Final output generated with ${finalOutput.accuracyScore}% accuracy`);

      return finalOutput;

    } catch (error) {
      console.error('❌ Final output generation failed:', error);
      throw error;
    }
  }

  /**
   * Cross-validate fields with OCR text
   */
  crossValidateWithOCR(fields, ocrText) {
    const validation = {
      matches: 0,
      total: 0,
      confidence: 0
    };

    // Check if key fields appear in OCR text
    const fieldsToCheck = ['seller_name', 'buyer_name', 'invoice_number'];

    for (const field of fieldsToCheck) {
      if (fields[field]) {
        validation.total++;
        if (ocrText.toLowerCase().includes(fields[field].toLowerCase())) {
          validation.matches++;
        }
      }
    }

    validation.confidence = validation.total > 0 ?
      (validation.matches / validation.total) * 100 : 0;

    return validation;
  }

  /**
   * Validate field formats
   */
  validateFieldFormats(fields, errors) {
    // Validate amounts are numbers
    const amountFields = ['total_net', 'total_vat', 'total_gross'];
    for (const field of amountFields) {
      if (fields[field] && isNaN(parseFloat(fields[field]))) {
        errors.push(`Invalid amount format: ${field}`);
      }
    }

    // Validate date format
    if (fields.invoice_date && fields.invoice_date !== '') {
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(fields.invoice_date)) {
        errors.push('Invalid date format: invoice_date (expected YYYY-MM-DD)');
      }
    }
  }

  /**
   * Calculate accuracy score based on extracted fields
   */
  calculateAccuracyScore(fields) {
    const requiredFields = [
      'seller_name', 'buyer_name', 'total_gross', 'invoice_number', 'invoice_date'
    ];

    let score = 0;
    let maxScore = requiredFields.length * 20; // 20 points per field

    for (const field of requiredFields) {
      if (fields[field] && fields[field] !== '' && fields[field] !== 'Unknown Seller' && fields[field] !== 'Unknown Buyer') {
        score += 20;
      }
    }

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Convert PDF first page to image for OCR
   */
  async convertPdfToImage(file) {
    try {
      // This would need to be implemented using PDF.js canvas rendering
      // For now, return null to indicate conversion not available
      return null;
    } catch (error) {
      console.error('PDF to image conversion failed:', error);
      return null;
    }
  }

  /**
   * Get step results for debugging
   */
  getStepResults() {
    return Object.fromEntries(this.stepResults);
  }

  /**
   * Get step timings for performance analysis
   */
  getStepTimings() {
    return Object.fromEntries(this.stepTimings);
  }
}

// Export singleton instance
export const documentProcessingPipeline = new DocumentProcessingPipeline();
