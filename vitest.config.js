import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    // Test environment
    environment: 'jsdom',

    // Global setup
    globals: true,
    setupFiles: ['./tests/setup/vitest.setup.js'],

    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'lcov', 'json'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/',
        'dist/',
        'coverage/',
        'tests/',
        '**/*.config.js',
        '**/*.config.ts',
        'scripts/',
        'docs/',
        'public/',
        '**/*.d.ts',
        '**/*.test.{js,jsx,ts,tsx}',
        '**/*.spec.{js,jsx,ts,tsx}'
      ],
      include: [
        'src/**/*.{js,jsx,ts,tsx}'
      ],
      // Enforce coverage thresholds
      thresholds: {
        global: {
          branches: 90,
          functions: 90,
          lines: 90,
          statements: 90
        }
      },
      // Fail if coverage is below thresholds
      skipFull: false
    },

    // Test file patterns
    include: [
      'tests/unit/**/*.{test,spec}.{js,jsx,ts,tsx}',
      'src/**/*.{test,spec}.{js,jsx,ts,tsx}'
    ],

    // Test timeout
    testTimeout: 10000,

    // Retry failed tests
    retry: 2,

    // Reporter configuration
    reporter: ['verbose'],

    // Mock configuration
    deps: {
      optimizer: {
        web: {
          include: ['@testing-library/jest-dom']
        }
      },
      inline: [
        'vitest-canvas-mock'
      ]
    },

    // Browser-like environment for Chrome extension testing
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: true
      }
    }
  },

  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@tests': path.resolve(__dirname, './tests'),
      '@docs': path.resolve(__dirname, './docs')
    }
  },

  // Define global variables for Chrome extension environment
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'test'),
    global: 'globalThis'
  },

  // Server configuration to handle problematic modules
  server: {
    deps: {
      external: ['canvas', 'sharp', 'jimp']
    }
  }
});
