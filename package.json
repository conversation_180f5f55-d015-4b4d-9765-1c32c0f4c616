{"name": "accounting-chrome-extension", "version": "1.0.0", "description": "Multi-VAT Chrome Extension for invoice processing with PDF.js, Tesseract.js, and OpenAI", "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}, "scripts": {"dev": "vite build --watch --mode development", "dev:build": "vite build --mode development", "build": "vite build --mode production", "preview": "vite preview", "test": "npm run test:all", "test:unit": "vitest run --coverage", "test:unit:watch": "vitest", "test:unit:jest": "jest --coverage --watchAll=false", "test:functional": "node tests/test-runner.js && node tests/functional/api/apiTestSuite.js", "test:e2e": "playwright test --project=chrome-extension", "test:visual": "python3 tests/visual/visual_test_runner.py", "test:all": "npm run test:unit && npm run test:functional && npm run test:e2e && npm run test:visual", "test:coverage": "vitest run --coverage --reporter=html --reporter=lcov --reporter=text", "lint": "eslint src/ tests/ --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src/ tests/ --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,md}\" \"tests/**/*.{js,jsx,ts,tsx}\" \"docs/**/*.md\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,css,md}\" \"tests/**/*.{js,jsx,ts,tsx}\" \"docs/**/*.md\"", "typecheck": "tsc --noEmit", "analyze": "npm run lint && npm run format:check && npm run typecheck", "pre-commit": "npm run analyze && npm run test:all", "prepare": "husky install", "postinstall": "husky install"}, "dependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.10", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "file-type": "^21.0.0", "lodash": "^4.17.21", "openai": "^4.24.1", "pdfjs-dist": "^3.11.174", "pretty-bytes": "^7.0.0", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-router-dom": "^6.8.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^2.15.3", "tesseract.js": "^4.1.4", "uuid": "^11.1.0"}, "devDependencies": {"@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.0", "@types/chrome": "^0.0.254", "@types/jest": "^30.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^2.1.9", "autoprefixer": "^10.4.16", "babel-jest": "^30.0.0-beta.3", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "happy-dom": "^17.5.6", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.0", "jest-chrome": "*", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "pixelmatch": "^5.3.0", "playwright": "^1.40.0", "pngjs": "^7.0.0", "postcss": "^8.4.32", "prettier": "^3.1.1", "rimraf": "^5.0.5", "selenium-webdriver": "^4.16.0", "tailwindcss": "^4.0.0-alpha.4", "typescript": "^5.3.0", "vite": "^5.0.0", "vitest": "^2.1.9", "vitest-canvas-mock": "^0.3.3", "wait-on": "^7.2.0"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "tests/**/*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "docs/**/*.md": ["prettier --write"], "*.{js,jsx,ts,tsx,css,md}": ["prettier --write"]}, "browserslist": ["last 2 Chrome versions"], "keywords": ["chrome-extension", "vat", "invoice", "pdf", "ocr", "ai", "react", "vite", "tailwindcss"], "author": "MVAT Team", "license": "MIT"}