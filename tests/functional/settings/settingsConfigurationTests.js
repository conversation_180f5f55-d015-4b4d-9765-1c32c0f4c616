#!/usr/bin/env node

/**
 * Settings Configuration Loading Tests
 * Tests comprehensive settings loading with environment configuration
 */

import { settingsService } from '../../../src/services/SettingsService.js';
import { environmentConfig } from '../../../src/services/EnvironmentConfigService.js';

console.log('🧪 SETTINGS CONFIGURATION LOADING TESTS');
console.log('============================================================');

/**
 * Test environment configuration initialization
 */
async function testEnvironmentConfigInitialization() {
  console.log('\n🔧 Test 1: Environment Configuration Initialization');
  
  try {
    const initialized = await environmentConfig.initialize();
    
    if (!initialized) {
      throw new Error('Environment configuration failed to initialize');
    }
    
    console.log('✅ Environment configuration initialized successfully');
    
    // Test company info loading
    const companyInfo = environmentConfig.getCompanyInfo();
    console.log('📋 Company info loaded:', {
      hasName: !!companyInfo?.name,
      hasNip: !!companyInfo?.nip,
      hasEmail: !!companyInfo?.contact?.email,
      hasPhone: !!companyInfo?.contact?.phone
    });
    
    // Test API keys loading
    const apiKeys = environmentConfig.get('apiKeys', {});
    console.log('🔑 API keys loaded:', {
      deepseek: !!apiKeys.deepseek?.key,
      openai: !!apiKeys.openai?.key,
      fakturownia: !!apiKeys.fakturownia?.token,
      infakt: !!apiKeys.infakt?.key
    });
    
    return true;
  } catch (error) {
    console.error('❌ Environment configuration initialization failed:', error.message);
    return false;
  }
}

/**
 * Test settings service loading with environment configuration
 */
async function testSettingsServiceLoading() {
  console.log('\n🔧 Test 2: Settings Service Loading with Environment Configuration');
  
  try {
    const settings = await settingsService.loadSettings();
    
    if (!settings) {
      throw new Error('Settings failed to load');
    }
    
    console.log('✅ Settings loaded successfully');
    
    // Test company settings
    console.log('🏢 Company settings:', {
      name: settings.company?.name || 'NOT SET',
      taxId: settings.company?.taxId || 'NOT SET',
      email: settings.company?.email || 'NOT SET',
      phone: settings.company?.phone || 'NOT SET',
      address: settings.company?.address || 'NOT SET'
    });
    
    // Test API key settings
    console.log('🔑 API key settings:', {
      deepseek: settings.apiKeys?.deepseek ? 'SET' : 'NOT SET',
      openai: settings.apiKeys?.openai ? 'SET' : 'NOT SET',
      fakturownia: settings.apiKeys?.fakturownia ? 'SET' : 'NOT SET',
      infakt: settings.apiKeys?.infakt ? 'SET' : 'NOT SET'
    });
    
    // Test display settings
    console.log('🎨 Display settings:', {
      currency: settings.display?.currency || 'NOT SET',
      language: settings.display?.language || 'NOT SET',
      dateFormat: settings.display?.dateFormat || 'NOT SET'
    });
    
    // Verify that environment configuration was merged
    const hasCompanyData = !!(settings.company?.name && settings.company?.name !== '');
    const hasApiKeys = !!(settings.apiKeys?.deepseek && settings.apiKeys?.deepseek !== '');
    
    if (!hasCompanyData) {
      console.warn('⚠️ Company data not properly loaded from environment configuration');
    }
    
    if (!hasApiKeys) {
      console.warn('⚠️ API keys not properly loaded from environment configuration');
    }
    
    return hasCompanyData && hasApiKeys;
  } catch (error) {
    console.error('❌ Settings service loading failed:', error.message);
    return false;
  }
}

/**
 * Test settings merging with environment configuration
 */
async function testSettingsMerging() {
  console.log('\n🔧 Test 3: Settings Merging with Environment Configuration');
  
  try {
    // Initialize environment config first
    await environmentConfig.initialize();
    
    // Get default settings
    const defaultSettings = settingsService.getDefaultSettings();
    
    // Test merging
    const mergedSettings = settingsService.mergeWithEnvironmentConfig(defaultSettings);
    
    console.log('✅ Settings merging completed');
    
    // Compare before and after merging
    const beforeCompanyName = defaultSettings.company?.name || 'EMPTY';
    const afterCompanyName = mergedSettings.company?.name || 'EMPTY';
    
    const beforeDeepSeekKey = defaultSettings.apiKeys?.deepseek || 'EMPTY';
    const afterDeepSeekKey = mergedSettings.apiKeys?.deepseek || 'EMPTY';
    
    console.log('📊 Merging results:', {
      companyName: { before: beforeCompanyName, after: afterCompanyName },
      deepseekKey: { 
        before: beforeDeepSeekKey === 'EMPTY' ? 'EMPTY' : 'SET',
        after: afterDeepSeekKey === 'EMPTY' ? 'EMPTY' : 'SET'
      }
    });
    
    // Verify merging worked
    const mergingWorked = afterCompanyName !== beforeCompanyName || afterDeepSeekKey !== beforeDeepSeekKey;
    
    if (!mergingWorked) {
      console.warn('⚠️ Settings merging may not be working properly');
    }
    
    return mergingWorked;
  } catch (error) {
    console.error('❌ Settings merging test failed:', error.message);
    return false;
  }
}

/**
 * Test API key loading for all providers
 */
async function testApiKeyLoading() {
  console.log('\n🔧 Test 4: API Key Loading for All Providers');
  
  try {
    const settings = await settingsService.loadSettings();
    
    const providers = ['deepseek', 'openai', 'fakturownia', 'infakt'];
    const results = {};
    
    for (const provider of providers) {
      const apiKey = await settingsService.getApiKey(provider);
      results[provider] = apiKey ? 'LOADED' : 'NOT LOADED';
    }
    
    console.log('🔑 API key loading results:', results);
    
    // Check if at least DeepSeek is loaded (it has a default value)
    const deepseekLoaded = results.deepseek === 'LOADED';
    
    if (!deepseekLoaded) {
      console.warn('⚠️ DeepSeek API key not loaded - this should have a default value');
    }
    
    return deepseekLoaded;
  } catch (error) {
    console.error('❌ API key loading test failed:', error.message);
    return false;
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🎯 Running comprehensive settings configuration tests...\n');
  
  const tests = [
    { name: 'Environment Config Initialization', fn: testEnvironmentConfigInitialization },
    { name: 'Settings Service Loading', fn: testSettingsServiceLoading },
    { name: 'Settings Merging', fn: testSettingsMerging },
    { name: 'API Key Loading', fn: testApiKeyLoading }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      const result = await test.fn();
      if (result) {
        console.log(`✅ ${test.name}: PASSED`);
        passed++;
      } else {
        console.log(`❌ ${test.name}: FAILED`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name}: ERROR - ${error.message}`);
      failed++;
    }
  }
  
  console.log('\n📊 SETTINGS CONFIGURATION TEST SUMMARY');
  console.log('============================================================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('🎉 ALL SETTINGS CONFIGURATION TESTS PASSED!');
    process.exit(0);
  } else {
    console.log('💥 SOME SETTINGS CONFIGURATION TESTS FAILED!');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllTests().catch(error => {
    console.error('💥 Test execution failed:', error);
    process.exit(1);
  });
}

export { runAllTests };
