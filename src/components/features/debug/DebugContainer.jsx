/**
 * DebugContainer - Debug interface for multi-step document processing pipeline
 * Allows manual execution of individual pipeline steps and viewing results
 */

import React, { useState, useRef } from 'react';
import { documentProcessingPipeline } from '../../../services/DocumentProcessingPipeline.js';
import { settingsService } from '../../../services/SettingsService.js';

const DebugContainer = ({ isVisible = false, onToggle }) => {
  const [debugState, setDebugState] = useState({
    selectedFile: null,
    currentStep: null,
    stepResults: {},
    stepTimings: {},
    isProcessing: false,
    logs: []
  });

  const fileInputRef = useRef(null);

  const pipelineSteps = [
    { id: 'pdf_extraction', name: 'PDF Text Extraction', description: 'Extract text from PDF using PDF.js' },
    { id: 'deepseek_analysis', name: 'DeepSeek AI Analysis', description: 'Analyze document with AI for field extraction' },
    { id: 'tesseract_reference', name: 'Tesseract OCR Reference', description: 'Extract OCR text for structural validation' },
    { id: 'field_mapping', name: 'Field Mapping', description: 'Map fields using configuration files' },
    { id: 'data_validation', name: 'Data Validation', description: 'Validate extracted data and calculate accuracy' },
    { id: 'final_output', name: 'Final Output', description: 'Generate final structured output' }
  ];

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setDebugState(prev => ({
      ...prev,
      logs: [...prev.logs, { timestamp, message, type }]
    }));
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      setDebugState(prev => ({
        ...prev,
        selectedFile: file,
        stepResults: {},
        stepTimings: {},
        logs: []
      }));
      addLog(`File selected: ${file.name} (${(file.size / 1024).toFixed(2)} KB)`, 'success');
    }
  };

  const runCompleteePipeline = async () => {
    if (!debugState.selectedFile) {
      addLog('Please select a file first', 'error');
      return;
    }

    setDebugState(prev => ({ ...prev, isProcessing: true }));
    addLog('Starting complete pipeline processing...', 'info');

    try {
      const settings = await settingsService.getSettings();
      const apiKey = settings.deepseek_api_key;

      const result = await documentProcessingPipeline.processDocument(debugState.selectedFile, {
        apiKey,
        language: 'pol',
        progressCallback: (progress) => {
          addLog(`Pipeline progress: ${progress.stage} - ${progress.progress}%`, 'info');
        }
      });

      if (result.success) {
        setDebugState(prev => ({
          ...prev,
          stepResults: result.stepResults,
          stepTimings: result.stepTimings,
          isProcessing: false
        }));
        addLog(`Pipeline completed successfully! Accuracy: ${result.finalResult.accuracyScore}%`, 'success');
      } else {
        addLog(`Pipeline failed: ${result.error}`, 'error');
        setDebugState(prev => ({ ...prev, isProcessing: false }));
      }
    } catch (error) {
      addLog(`Pipeline error: ${error.message}`, 'error');
      setDebugState(prev => ({ ...prev, isProcessing: false }));
    }
  };

  const runSingleStep = async (stepId) => {
    if (!debugState.selectedFile) {
      addLog('Please select a file first', 'error');
      return;
    }

    setDebugState(prev => ({ ...prev, currentStep: stepId, isProcessing: true }));
    addLog(`Running single step: ${stepId}...`, 'info');

    try {
      // This would need to be implemented to run individual steps
      addLog(`Single step execution not yet implemented for: ${stepId}`, 'warning');
      setDebugState(prev => ({ ...prev, currentStep: null, isProcessing: false }));
    } catch (error) {
      addLog(`Step error: ${error.message}`, 'error');
      setDebugState(prev => ({ ...prev, currentStep: null, isProcessing: false }));
    }
  };

  const clearLogs = () => {
    setDebugState(prev => ({ ...prev, logs: [] }));
  };

  const downloadStepResults = () => {
    const data = {
      file: debugState.selectedFile ? {
        name: debugState.selectedFile.name,
        size: debugState.selectedFile.size,
        type: debugState.selectedFile.type
      } : null,
      stepResults: debugState.stepResults,
      stepTimings: debugState.stepTimings,
      logs: debugState.logs,
      timestamp: new Date().toISOString()
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `debug-results-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  if (!isVisible) return null;

  return (
    <div className="debug-container bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-4 mt-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold text-gray-800">🛠️ Debug Interface - Multi-Step Pipeline</h3>
        <button
          onClick={onToggle}
          className="px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 text-sm"
        >
          Hide Debug
        </button>
      </div>

      {/* File Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Test File:
        </label>
        <input
          ref={fileInputRef}
          type="file"
          accept=".pdf"
          onChange={handleFileSelect}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        {debugState.selectedFile && (
          <p className="text-sm text-gray-600 mt-1">
            Selected: {debugState.selectedFile.name} ({(debugState.selectedFile.size / 1024).toFixed(2)} KB)
          </p>
        )}
      </div>

      {/* Pipeline Controls */}
      <div className="mb-4">
        <div className="flex gap-2 mb-2">
          <button
            onClick={runCompleteePipeline}
            disabled={!debugState.selectedFile || debugState.isProcessing}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {debugState.isProcessing ? '⏳ Processing...' : '🚀 Run Complete Pipeline'}
          </button>
          <button
            onClick={downloadStepResults}
            disabled={Object.keys(debugState.stepResults).length === 0}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            📥 Download Results
          </button>
          <button
            onClick={clearLogs}
            className="px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
          >
            🗑️ Clear Logs
          </button>
        </div>
      </div>

      {/* Pipeline Steps */}
      <div className="mb-4">
        <h4 className="text-md font-medium text-gray-700 mb-2">Pipeline Steps:</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          {pipelineSteps.map((step) => (
            <div key={step.id} className="border border-gray-300 rounded p-2">
              <div className="flex justify-between items-start mb-1">
                <h5 className="text-sm font-medium text-gray-800">{step.name}</h5>
                {debugState.stepResults[step.id] && (
                  <span className="text-xs bg-green-100 text-green-800 px-1 rounded">✓</span>
                )}
              </div>
              <p className="text-xs text-gray-600 mb-2">{step.description}</p>
              <button
                onClick={() => runSingleStep(step.id)}
                disabled={!debugState.selectedFile || debugState.isProcessing}
                className="w-full px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed"
              >
                {debugState.currentStep === step.id ? '⏳' : '▶️'} Run Step
              </button>
              {debugState.stepTimings[step.id] && (
                <p className="text-xs text-gray-500 mt-1">
                  Time: {debugState.stepTimings[step.id].toFixed(2)}ms
                </p>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step Results */}
      {Object.keys(debugState.stepResults).length > 0 && (
        <div className="mb-4">
          <h4 className="text-md font-medium text-gray-700 mb-2">Step Results:</h4>
          <div className="max-h-60 overflow-y-auto bg-white border border-gray-300 rounded p-2">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(debugState.stepResults, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Debug Logs */}
      <div>
        <h4 className="text-md font-medium text-gray-700 mb-2">Debug Logs:</h4>
        <div className="max-h-40 overflow-y-auto bg-black text-green-400 font-mono text-xs p-2 rounded">
          {debugState.logs.length === 0 ? (
            <p className="text-gray-500">No logs yet...</p>
          ) : (
            debugState.logs.map((log, index) => (
              <div key={index} className={`mb-1 ${
                log.type === 'error' ? 'text-red-400' : 
                log.type === 'success' ? 'text-green-400' : 
                log.type === 'warning' ? 'text-yellow-400' : 'text-gray-300'
              }`}>
                <span className="text-gray-500">[{log.timestamp}]</span> {log.message}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default DebugContainer;
