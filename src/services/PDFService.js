/**
 * PDF Service for handling PDF document processing
 * Uses PDF.js for text extraction
 */

import * as pdfjs from 'pdfjs-dist';

class PDFService {
  constructor() {
    // Initialize PDF.js worker
    pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;
  }

  /**
   * Extract text content from PDF file
   * @param {File|Blob} file - PDF file to process
   * @returns {Promise<{text: string, pages: number, metadata: Object}>}
   */
  async extractText(file) {
    try {
      // Load the PDF file
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;

      // Get document metadata
      const metadata = await pdf.getMetadata();

      // Extract text from all pages
      const textContent = [];
      for (let i = 1; i <= pdf.numPages; i++) {
        const page = await pdf.getPage(i);
        const content = await page.getTextContent();
        const text = content.items.map(item => 'str' in item ? item.str : '').join(' ');
        textContent.push(text);
      }

      return {
        text: textContent.join('\\n'),
        pages: pdf.numPages,
        metadata: metadata.info || {}
      };
    } catch (error) {
      console.error('Error extracting PDF text:', error);
      throw new Error('Failed to extract text from PDF');
    }
  }

  /**
   * Get PDF document metadata
   * @param {File|Blob} file - PDF file
   * @returns {Promise<Object>} - PDF metadata
   */
  async getMetadata(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
      return await pdf.getMetadata();
    } catch (error) {
      console.error('Error getting PDF metadata:', error);
      throw new Error('Failed to get PDF metadata');
    }
  }

  /**
   * Validate if file is a valid PDF
   * @param {File|Blob} file - File to validate
   * @returns {Promise<boolean>}
   */
  async validatePDF(file) {
    try {
      // Check file type
      if (!file.type || !file.type.includes('pdf')) {
        return false;
      }

      // Try to parse PDF structure
      const arrayBuffer = await file.arrayBuffer();
      await pdfjs.getDocument({ data: arrayBuffer }).promise;
      return true;
    } catch (error) {
      console.error('PDF validation failed:', error);
      return false;
    }
  }

  /**
   * Extract text from specific page
   * @param {File|Blob} file - PDF file
   * @param {number} pageNumber - Page number to extract (1-based)
   * @returns {Promise<string>} - Page text content
   */
  async extractPageText(file, pageNumber) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;

      if (pageNumber < 1 || pageNumber > pdf.numPages) {
        throw new Error('Invalid page number');
      }

      const page = await pdf.getPage(pageNumber);
      const content = await page.getTextContent();
      return content.items.map(item => 'str' in item ? item.str : '').join(' ');
    } catch (error) {
      console.error('Error extracting page text:', error);
      throw new Error('Failed to extract page text');
    }
  }

  /**
   * Get document structure information
   * @param {File|Blob} file - PDF file
   * @returns {Promise<Object>} - Document structure info
   */
  async getStructureInfo(file) {
    try {
      const arrayBuffer = await file.arrayBuffer();
      const pdf = await pdfjs.getDocument({ data: arrayBuffer }).promise;
      const outline = await pdf.getOutline();
      const metadata = await pdf.getMetadata();

      return {
        pages: pdf.numPages,
        hasOutline: !!outline && outline.length > 0,
        metadata: metadata.info || {},
        isTagged: metadata.metadata?.has('Marked') || false,
        isEncrypted: pdf._pdfInfo?.encrypted || false
      };
    } catch (error) {
      console.error('Error getting structure info:', error);
      throw new Error('Failed to get document structure info');
    }
  }
}

export default PDFService;
