/**
 * Enhanced PDF Service for 90% Accuracy Target
 * Improved PDF text extraction with multiple methods and validation
 * 
 * Features:
 * - Enhanced PDF.js text extraction
 * - OCR fallback with Tesseract.js
 * - Text quality assessment
 * - Multi-method validation
 * - Performance optimization
 */

import * as pdfjsLib from 'pdfjs-dist';
import { sandboxCommunicationService } from './SandboxCommunicationService.js';
import { processingLogger } from '../utils/ProcessingLogger.js';

export class EnhancedPDFService {
  constructor() {
    this.initialized = false;
    this.workerSrc = null;
    this.maxPages = 50; // Limit for performance
    this.textQualityThreshold = 0.7; // Minimum text quality for PDF extraction
  }

  /**
   * Initialize PDF.js with optimized settings
   */
  async initialize() {
    if (this.initialized) return true;

    try {
      // Set up PDF.js worker
      if (typeof window !== 'undefined') {
        // Browser environment
        this.workerSrc = '/assets/pdf.worker.min.js';
        pdfjsLib.GlobalWorkerOptions.workerSrc = this.workerSrc;
      } else {
        // Node.js environment
        const pdfjsWorker = await import('pdfjs-dist/build/pdf.worker.entry');
        pdfjsLib.GlobalWorkerOptions.workerSrc = pdfjsWorker;
      }

      this.initialized = true;
      console.log('✅ Enhanced PDF Service initialized');
      return true;

    } catch (error) {
      console.error('❌ Enhanced PDF Service initialization failed:', error);
      return false;
    }
  }

  /**
   * Extract text from PDF with enhanced accuracy
   * @param {File} file - PDF file
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} Extraction result
   */
  async extractText(file, options = {}) {
    const {
      onProgress = null,
      trackingId = null,
      enableOCRFallback = true,
      qualityThreshold = this.textQualityThreshold
    } = options;

    if (!await this.initialize()) {
      throw new Error('PDF service initialization failed');
    }

    const uploadId = trackingId || processingLogger.generateUploadId();
    
    try {
      processingLogger.logStep(uploadId, 'pdf_extraction_start', {
        fileName: file.name,
        fileSize: file.size,
        enableOCRFallback
      });

      if (onProgress) {
        onProgress({ stage: 'pdf_loading', progress: 10 });
      }

      // Load PDF document
      const arrayBuffer = await file.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      const loadingTask = pdfjsLib.getDocument({
        data: uint8Array,
        verbosity: 0, // Reduce console output
        maxImageSize: 1024 * 1024, // 1MB max image size
        disableFontFace: true, // Improve performance
        disableRange: false,
        disableStream: false
      });

      const pdf = await loadingTask.promise;

      if (onProgress) {
        onProgress({ stage: 'pdf_extracting', progress: 30 });
      }

      // Extract text from all pages
      const extractionResult = await this.extractTextFromPages(pdf, {
        onProgress,
        uploadId,
        maxPages: this.maxPages
      });

      // Assess text quality
      const qualityAssessment = this.assessTextQuality(extractionResult.text);
      
      processingLogger.logStep(uploadId, 'pdf_extraction_complete', {
        textLength: extractionResult.text.length,
        pageCount: extractionResult.pageCount,
        quality: qualityAssessment,
        method: 'pdf_text'
      });

      // Check if OCR fallback is needed
      if (enableOCRFallback && qualityAssessment.score < qualityThreshold) {
        console.log('📄 PDF text quality insufficient, attempting OCR fallback...');
        
        if (onProgress) {
          onProgress({ stage: 'ocr_fallback', progress: 70 });
        }

        const ocrResult = await this.performOCRFallback(file, {
          onProgress,
          uploadId
        });

        if (ocrResult && ocrResult.text.length > extractionResult.text.length) {
          processingLogger.logStep(uploadId, 'ocr_fallback_success', {
            originalLength: extractionResult.text.length,
            ocrLength: ocrResult.text.length
          });

          return {
            text: ocrResult.text,
            method: 'pdf_ocr',
            quality: this.assessTextQuality(ocrResult.text),
            pageCount: extractionResult.pageCount,
            metadata: {
              ...extractionResult.metadata,
              ocrUsed: true,
              originalTextLength: extractionResult.text.length
            }
          };
        }
      }

      if (onProgress) {
        onProgress({ stage: 'pdf_complete', progress: 100 });
      }

      return {
        text: extractionResult.text,
        method: 'pdf_text',
        quality: qualityAssessment,
        pageCount: extractionResult.pageCount,
        metadata: {
          ...extractionResult.metadata,
          ocrUsed: false
        }
      };

    } catch (error) {
      processingLogger.logStep(uploadId, 'pdf_extraction_error', {
        error: error.message,
        stack: error.stack
      });

      console.error('❌ Enhanced PDF extraction failed:', error);
      throw new Error(`Enhanced PDF extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract text from PDF pages with optimization
   * @param {Object} pdf - PDF document
   * @param {Object} options - Options
   * @returns {Promise<Object>} Extraction result
   */
  async extractTextFromPages(pdf, options = {}) {
    const { onProgress, uploadId, maxPages } = options;
    const numPages = Math.min(pdf.numPages, maxPages);
    
    let fullText = '';
    const pageTexts = [];
    const metadata = {
      totalPages: pdf.numPages,
      processedPages: numPages,
      truncated: pdf.numPages > maxPages
    };

    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      try {
        const page = await pdf.getPage(pageNum);
        const textContent = await page.getTextContent({
          normalizeWhitespace: true,
          disableCombineTextItems: false
        });

        // Extract text items and combine
        const pageText = textContent.items
          .map(item => item.str)
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim();

        pageTexts.push(pageText);
        fullText += pageText + '\n';

        // Update progress
        if (onProgress) {
          const progress = 30 + Math.round((pageNum / numPages) * 40);
          onProgress({ 
            stage: 'pdf_extracting', 
            progress,
            currentPage: pageNum,
            totalPages: numPages
          });
        }

        // Log progress for large documents
        if (pageNum % 10 === 0 && uploadId) {
          processingLogger.logStep(uploadId, 'pdf_page_progress', {
            currentPage: pageNum,
            totalPages: numPages,
            textLength: fullText.length
          });
        }

      } catch (pageError) {
        console.warn(`⚠️ Failed to extract text from page ${pageNum}:`, pageError);
        pageTexts.push('');
      }
    }

    return {
      text: fullText.trim(),
      pageTexts,
      pageCount: numPages,
      metadata
    };
  }

  /**
   * Assess text quality for extraction validation
   * @param {string} text - Extracted text
   * @returns {Object} Quality assessment
   */
  assessTextQuality(text) {
    if (!text || typeof text !== 'string') {
      return { score: 0, issues: ['No text extracted'] };
    }

    const issues = [];
    let score = 100;

    // Length check
    if (text.length < 50) {
      issues.push('Text too short');
      score -= 30;
    }

    // Character diversity check
    const uniqueChars = new Set(text.toLowerCase()).size;
    if (uniqueChars < 10) {
      issues.push('Low character diversity');
      score -= 20;
    }

    // Word count check
    const words = text.split(/\s+/).filter(word => word.length > 0);
    if (words.length < 10) {
      issues.push('Too few words');
      score -= 25;
    }

    // Invoice keyword check
    const invoiceKeywords = [
      'faktura', 'invoice', 'numer', 'number', 'data', 'date',
      'sprzedawca', 'seller', 'nabywca', 'buyer', 'suma', 'total',
      'vat', 'podatek', 'kwota', 'amount'
    ];

    const foundKeywords = invoiceKeywords.filter(keyword => 
      text.toLowerCase().includes(keyword.toLowerCase())
    );

    if (foundKeywords.length < 3) {
      issues.push('Few invoice-related keywords');
      score -= 15;
    }

    // Numeric content check
    const numbers = text.match(/\d+/g);
    if (!numbers || numbers.length < 3) {
      issues.push('Insufficient numeric content');
      score -= 10;
    }

    return {
      score: Math.max(0, score),
      issues,
      metrics: {
        length: text.length,
        wordCount: words.length,
        uniqueChars,
        keywordCount: foundKeywords.length,
        numberCount: numbers ? numbers.length : 0
      }
    };
  }

  /**
   * Perform OCR fallback using Tesseract.js
   * @param {File} file - PDF file
   * @param {Object} options - OCR options
   * @returns {Promise<Object>} OCR result
   */
  async performOCRFallback(file, options = {}) {
    const { onProgress, uploadId } = options;

    try {
      console.log('🔍 Starting OCR fallback with Tesseract.js...');
      
      if (uploadId) {
        processingLogger.logStep(uploadId, 'ocr_fallback_start', {
          fileName: file.name
        });
      }

      // Use sandbox communication service for OCR
      const ocrResult = await sandboxCommunicationService.performOCR(file, {
        onProgress: (progress) => {
          if (onProgress) {
            onProgress({
              stage: 'ocr_processing',
              progress: 70 + Math.round(progress * 0.25) // 70-95% range
            });
          }
        }
      });

      if (uploadId) {
        processingLogger.logStep(uploadId, 'ocr_fallback_complete', {
          textLength: ocrResult.text ? ocrResult.text.length : 0,
          confidence: ocrResult.confidence || 0
        });
      }

      return ocrResult;

    } catch (error) {
      console.error('❌ OCR fallback failed:', error);
      
      if (uploadId) {
        processingLogger.logStep(uploadId, 'ocr_fallback_error', {
          error: error.message
        });
      }

      return null;
    }
  }

  /**
   * Check if text contains invoice-related keywords
   * @param {string} text - Text to check
   * @returns {boolean} Whether text contains invoice keywords
   */
  containsInvoiceKeywords(text) {
    if (!text) return false;

    const keywords = [
      'faktura', 'invoice', 'numer', 'number', 'data', 'date',
      'sprzedawca', 'seller', 'nabywca', 'buyer', 'suma', 'total',
      'vat', 'podatek', 'kwota', 'amount', 'brutto', 'gross'
    ];

    const lowerText = text.toLowerCase();
    return keywords.some(keyword => lowerText.includes(keyword));
  }
}

// Export singleton instance
export const enhancedPDFService = new EnhancedPDFService();
export default enhancedPDFService;
