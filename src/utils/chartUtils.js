/**
 * Chart Utilities
 *
 * Utility functions for chart configuration, theming, and data formatting
 * for the analytics dashboard visualization components.
 */

/**
 * Chart color palette
 */
export const CHART_COLORS = {
  primary: '#3B82F6',
  secondary: '#10B981',
  accent: '#F59E0B',
  danger: '#EF4444',
  warning: '#F97316',
  info: '#06B6D4',
  success: '#22C55E',
  muted: '#6B7280',

  // Extended palette for multiple series
  palette: [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
    '#06B6D4', '#F97316', '#EC4899', '#84CC16', '#6366F1'
  ],

  // Gradient colors
  gradients: {
    blue: ['#3B82F6', '#1D4ED8'],
    green: ['#10B981', '#047857'],
    orange: ['#F59E0B', '#D97706'],
    red: ['#EF4444', '#DC2626'],
    purple: ['#8B5CF6', '#7C3AED']
  }
};

/**
 * Chart themes
 */
export const CHART_THEMES = {
  light: {
    backgroundColor: '#FFFFFF',
    textColor: '#1F2937',
    gridColor: '#E5E7EB',
    borderColor: '#D1D5DB',
    tooltipBackground: '#FFFFFF',
    tooltipBorder: '#D1D5DB',
    tooltipShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
  },
  dark: {
    backgroundColor: '#1F2937',
    textColor: '#F9FAFB',
    gridColor: '#374151',
    borderColor: '#4B5563',
    tooltipBackground: '#374151',
    tooltipBorder: '#4B5563',
    tooltipShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.3)'
  }
};

/**
 * Default chart configuration
 */
export const DEFAULT_CHART_CONFIG = {
  responsive: true,
  maintainAspectRatio: false,
  margin: { top: 20, right: 30, left: 20, bottom: 20 },
  grid: {
    strokeDasharray: '3 3',
    stroke: CHART_THEMES.light.gridColor
  },
  tooltip: {
    contentStyle: {
      backgroundColor: CHART_THEMES.light.tooltipBackground,
      border: `1px solid ${CHART_THEMES.light.tooltipBorder}`,
      borderRadius: '8px',
      boxShadow: CHART_THEMES.light.tooltipShadow
    }
  },
  legend: {
    verticalAlign: 'top',
    height: 36
  }
};

/**
 * Format number for display
 */
export function formatNumber(value, options = {}) {
  const {
    decimals = 0,
    prefix = '',
    suffix = '',
    compact = false,
    currency = false
  } = options;

  if (value === null || value === undefined || isNaN(value)) {
    return '—';
  }

  let formattedValue = value;

  // Handle compact notation (K, M, B)
  if (compact && Math.abs(value) >= 1000) {
    const units = ['', 'K', 'M', 'B', 'T'];
    const unitIndex = Math.floor(Math.log10(Math.abs(value)) / 3);
    formattedValue = value / Math.pow(1000, unitIndex);

    const formatted = formattedValue.toFixed(decimals);
    return `${prefix}${formatted}${units[unitIndex]}${suffix}`;
  }

  // Handle currency formatting
  if (currency) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    }).format(value);
  }

  // Standard number formatting
  const formatted = formattedValue.toFixed(decimals);
  return `${prefix}${formatted}${suffix}`;
}

/**
 * Format percentage for display
 */
export function formatPercentage(value, decimals = 1) {
  if (value === null || value === undefined || isNaN(value)) {
    return '—';
  }
  return `${value.toFixed(decimals)}%`;
}

/**
 * Format date for chart labels
 */
export function formatDateLabel(date, format = 'short') {
  if (!date) return '';

  const dateObj = typeof date === 'string' ? new Date(date) : date;

  switch (format) {
    case 'short':
      return dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    case 'month':
      return dateObj.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    case 'full':
      return dateObj.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    default:
      return dateObj.toLocaleDateString();
  }
}

/**
 * Generate chart configuration for line charts
 */
export function createLineChartConfig(options = {}) {
  const {
    theme = 'light',
    showGrid = true,
    showLegend = true,
    strokeWidth = 2,
    dotSize = 4
  } = options;

  const themeConfig = CHART_THEMES[theme];

  return {
    ...DEFAULT_CHART_CONFIG,
    grid: showGrid ? {
      ...DEFAULT_CHART_CONFIG.grid,
      stroke: themeConfig.gridColor
    } : false,
    legend: showLegend ? DEFAULT_CHART_CONFIG.legend : false,
    tooltip: {
      ...DEFAULT_CHART_CONFIG.tooltip,
      contentStyle: {
        ...DEFAULT_CHART_CONFIG.tooltip.contentStyle,
        backgroundColor: themeConfig.tooltipBackground,
        border: `1px solid ${themeConfig.tooltipBorder}`,
        color: themeConfig.textColor
      }
    },
    strokeWidth,
    dot: { r: dotSize }
  };
}

/**
 * Generate chart configuration for bar charts
 */
export function createBarChartConfig(options = {}) {
  const {
    theme = 'light',
    showGrid = true,
    showLegend = true,
    barSize = 20
  } = options;

  const themeConfig = CHART_THEMES[theme];

  return {
    ...DEFAULT_CHART_CONFIG,
    grid: showGrid ? {
      ...DEFAULT_CHART_CONFIG.grid,
      stroke: themeConfig.gridColor
    } : false,
    legend: showLegend ? DEFAULT_CHART_CONFIG.legend : false,
    tooltip: {
      ...DEFAULT_CHART_CONFIG.tooltip,
      contentStyle: {
        ...DEFAULT_CHART_CONFIG.tooltip.contentStyle,
        backgroundColor: themeConfig.tooltipBackground,
        border: `1px solid ${themeConfig.tooltipBorder}`,
        color: themeConfig.textColor
      }
    },
    barSize
  };
}

/**
 * Generate chart configuration for pie charts
 */
export function createPieChartConfig(options = {}) {
  const {
    theme = 'light',
    showLegend = true,
    innerRadius = 0,
    outerRadius = 80
  } = options;

  const themeConfig = CHART_THEMES[theme];

  return {
    ...DEFAULT_CHART_CONFIG,
    legend: showLegend ? {
      ...DEFAULT_CHART_CONFIG.legend,
      wrapperStyle: { color: themeConfig.textColor }
    } : false,
    tooltip: {
      ...DEFAULT_CHART_CONFIG.tooltip,
      contentStyle: {
        ...DEFAULT_CHART_CONFIG.tooltip.contentStyle,
        backgroundColor: themeConfig.tooltipBackground,
        border: `1px solid ${themeConfig.tooltipBorder}`,
        color: themeConfig.textColor
      }
    },
    innerRadius,
    outerRadius
  };
}

/**
 * Get color for chart series
 */
export function getSeriesColor(index, total = 1) {
  if (total === 1) {
    return CHART_COLORS.primary;
  }

  return CHART_COLORS.palette[index % CHART_COLORS.palette.length];
}

/**
 * Generate gradient definition for charts
 */
export function createGradient(id, colors, direction = 'vertical') {
  const [startColor, endColor] = colors;

  return {
    id,
    x1: direction === 'horizontal' ? '0%' : '0%',
    y1: direction === 'horizontal' ? '0%' : '0%',
    x2: direction === 'horizontal' ? '100%' : '0%',
    y2: direction === 'horizontal' ? '0%' : '100%',
    stops: [
      { offset: '0%', stopColor: startColor, stopOpacity: 0.8 },
      { offset: '100%', stopColor: endColor, stopOpacity: 0.3 }
    ]
  };
}

/**
 * Custom tooltip formatter configuration
 */
export function createCustomTooltip(options = {}) {
  const {
    labelFormatter = (label) => label,
    valueFormatter = (value) => formatNumber(value),
    showLabel = true,
    showValue = true
  } = options;

  return {
    labelFormatter,
    valueFormatter,
    showLabel,
    showValue,
    contentStyle: {
      backgroundColor: '#ffffff',
      border: '1px solid #e5e7eb',
      borderRadius: '8px',
      boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
    }
  };
}

/**
 * Calculate chart dimensions based on container
 */
export function calculateChartDimensions(containerWidth, containerHeight, aspectRatio = 16/9) {
  const maxWidth = containerWidth - 40; // Account for padding
  const maxHeight = containerHeight - 40;

  let width = maxWidth;
  let height = width / aspectRatio;

  if (height > maxHeight) {
    height = maxHeight;
    width = height * aspectRatio;
  }

  return { width: Math.max(width, 300), height: Math.max(height, 200) };
}

/**
 * Animate chart data changes
 */
export function createAnimationConfig(duration = 1000, easing = 'ease-out') {
  return {
    duration,
    easing,
    begin: 0,
    isActive: true
  };
}

/**
 * Export chart as image
 */
export function exportChartAsImage(chartRef, filename = 'chart', format = 'png') {
  if (!chartRef.current) return;

  // This would need to be implemented based on the specific chart library
  // For recharts, you might use html2canvas or similar
  console.log(`Exporting chart as ${format}:`, filename);
}
