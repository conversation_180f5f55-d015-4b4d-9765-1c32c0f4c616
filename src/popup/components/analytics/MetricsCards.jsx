/**
 * Metrics Cards Component
 * 
 * Displays key performance indicators and metrics in card format
 * for the analytics dashboard.
 */

import React from 'react';
import { formatNumber, formatPercentage } from '../../../utils/chartUtils.js';

/**
 * Individual metric card component
 */
const MetricCard = ({ 
  title, 
  value, 
  change, 
  changeType = 'neutral',
  icon,
  subtitle,
  formatter = formatNumber,
  className = ''
}) => {
  const getChangeColor = () => {
    switch (changeType) {
      case 'positive': return 'text-green-600';
      case 'negative': return 'text-red-600';
      case 'warning': return 'text-yellow-600';
      default: return 'text-gray-600';
    }
  };

  const getChangeIcon = () => {
    if (changeType === 'positive') return '↗';
    if (changeType === 'negative') return '↘';
    return '→';
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mb-1">
            {formatter(value)}
          </p>
          {subtitle && (
            <p className="text-xs text-gray-500">{subtitle}</p>
          )}
        </div>
        {icon && (
          <div className="flex-shrink-0 ml-4">
            <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
              <span className="text-blue-600 text-xl">{icon}</span>
            </div>
          </div>
        )}
      </div>
      
      {change !== undefined && change !== null && (
        <div className="mt-4 flex items-center">
          <span className={`text-sm font-medium ${getChangeColor()}`}>
            {getChangeIcon()} {Math.abs(change)}%
          </span>
          <span className="text-sm text-gray-500 ml-2">vs last period</span>
        </div>
      )}
    </div>
  );
};

/**
 * Main metrics cards component
 */
const MetricsCards = ({ data, className = '' }) => {
  if (!data) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-8 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  const { summary, documents, ai, business, system } = data;

  const metrics = [
    {
      title: 'Total Documents',
      value: documents?.total || 0,
      icon: '📄',
      subtitle: 'Documents processed',
      formatter: (val) => formatNumber(val, { compact: true })
    },
    {
      title: 'Success Rate',
      value: documents?.successRate || 0,
      icon: '✅',
      subtitle: 'Processing success',
      formatter: formatPercentage,
      changeType: documents?.successRate >= 90 ? 'positive' : 
                  documents?.successRate >= 70 ? 'warning' : 'negative'
    },
    {
      title: 'Total Value',
      value: business?.totalValue || 0,
      icon: '💰',
      subtitle: 'Invoice value processed',
      formatter: (val) => formatNumber(val, { currency: true, compact: true })
    },
    {
      title: 'AI Confidence',
      value: ai?.averageConfidence || 0,
      icon: '🤖',
      subtitle: 'Average AI confidence',
      formatter: formatPercentage,
      changeType: ai?.averageConfidence >= 85 ? 'positive' : 
                  ai?.averageConfidence >= 70 ? 'warning' : 'negative'
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {metrics.map((metric, index) => (
        <MetricCard
          key={index}
          title={metric.title}
          value={metric.value}
          icon={metric.icon}
          subtitle={metric.subtitle}
          formatter={metric.formatter}
          changeType={metric.changeType}
        />
      ))}
    </div>
  );
};

/**
 * Detailed metrics section
 */
export const DetailedMetrics = ({ data, className = '' }) => {
  if (!data) return null;

  const { documents, ai, business, system } = data;

  const detailedMetrics = [
    {
      category: 'Document Processing',
      metrics: [
        {
          label: 'Average Processing Time',
          value: documents?.averageProcessingTime || 0,
          formatter: (val) => `${formatNumber(val, { decimals: 1 })}s`
        },
        {
          label: 'Documents Today',
          value: documents?.todayCount || 0,
          formatter: formatNumber
        },
        {
          label: 'Error Rate',
          value: documents?.total > 0 ? ((documents?.total - documents?.successful) / documents?.total) * 100 : 0,
          formatter: formatPercentage
        }
      ]
    },
    {
      category: 'AI Analysis',
      metrics: [
        {
          label: 'Total Analyses',
          value: ai?.total || 0,
          formatter: formatNumber
        },
        {
          label: 'Average Analysis Time',
          value: ai?.averageAnalysisTime || 0,
          formatter: (val) => `${formatNumber(val, { decimals: 1 })}s`
        },
        {
          label: 'API Calls',
          value: ai?.apiUsage?.deepseek?.calls || 0,
          formatter: formatNumber
        }
      ]
    },
    {
      category: 'Business Intelligence',
      metrics: [
        {
          label: 'Average Invoice Value',
          value: business?.averageValue || 0,
          formatter: (val) => formatNumber(val, { currency: true })
        },
        {
          label: 'Unique Vendors',
          value: Object.keys(business?.valueByVendor || {}).length,
          formatter: formatNumber
        },
        {
          label: 'This Month Value',
          value: business?.thisMonthValue || 0,
          formatter: (val) => formatNumber(val, { currency: true, compact: true })
        }
      ]
    },
    {
      category: 'System Performance',
      metrics: [
        {
          label: 'Features Used',
          value: Object.keys(system?.featureUsage || {}).length,
          formatter: formatNumber
        },
        {
          label: 'Average Response Time',
          value: system?.averageResponseTime || 0,
          formatter: (val) => `${formatNumber(val, { decimals: 0 })}ms`
        },
        {
          label: 'Error Count',
          value: system?.errorRate || 0,
          formatter: formatNumber
        }
      ]
    }
  ];

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`}>
      {detailedMetrics.map((category, categoryIndex) => (
        <div key={categoryIndex} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">{category.category}</h3>
          <div className="space-y-3">
            {category.metrics.map((metric, metricIndex) => (
              <div key={metricIndex} className="flex justify-between items-center">
                <span className="text-sm text-gray-600">{metric.label}</span>
                <span className="text-sm font-medium text-gray-900">
                  {metric.formatter(metric.value)}
                </span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

/**
 * Quick stats component
 */
export const QuickStats = ({ data, className = '' }) => {
  if (!data) return null;

  const stats = [
    {
      label: 'Uptime',
      value: '99.9%',
      color: 'text-green-600'
    },
    {
      label: 'Last Updated',
      value: new Date(data.summary?.lastUpdated || Date.now()).toLocaleTimeString(),
      color: 'text-gray-600'
    },
    {
      label: 'Data Points',
      value: formatNumber((data.documents?.total || 0) + (data.ai?.total || 0)),
      color: 'text-blue-600'
    }
  ];

  return (
    <div className={`flex flex-wrap gap-6 ${className}`}>
      {stats.map((stat, index) => (
        <div key={index} className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">{stat.label}:</span>
          <span className={`text-sm font-medium ${stat.color}`}>{stat.value}</span>
        </div>
      ))}
    </div>
  );
};

export default MetricsCards;
